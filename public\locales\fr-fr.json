{"Favorite": "<PERSON><PERSON><PERSON>", "Tag": "Tag", "Duplicate": "<PERSON><PERSON><PERSON><PERSON>", "Persona": "<PERSON>a", "Delete": "<PERSON><PERSON><PERSON><PERSON>", "AI Response Configuration": "Configuration de la réponse de l'IA", "AI Configuration panel will stay open": "Le panneau de configuration de l'IA restera ouvert", "clickslidertips": "Cliquez sur le curseur pour saisir les valeurs manuellement.", "MAD LAB MODE ON": "MODE MAD LAB ACTIVÉ", "Documentation on sampling parameters": "Documentation sur les paramètres d'échantillonnage", "kobldpresets": "Presets de Kobold", "guikoboldaisettings": "Paramètres de l'interface utilisateur de KoboldAI", "Update current preset": "Mettre à jour le preset actuel", "Save preset as": "Enregistrer le preset sous", "Import preset": "Importer le preset", "Export preset": "Exporter le preset", "Restore current preset": "Restaurer le preset actuel", "Delete the preset": "Supprimer le preset", "novelaipresets": "Presets de NovelAI", "Default": "<PERSON><PERSON> <PERSON><PERSON>", "openaipresets": "Presets d'OpenAI", "Text Completion presets": "Presets de complétion de texte", "AI Module": "Module IA", "Changes the style of the generated text.": "Modifie le style du texte généré.", "No Module": "Aucun module", "Instruct": "Instruire", "Prose Augmenter": "Amélioration de prose", "Text Adventure": "Aventure textuelle", "response legth(tokens)": "<PERSON>ueur de la réponse (en tokens)", "Streaming": "Diffusion en continu", "Streaming_desc": "Afficher la réponse bit par bit au fur et à mesure de sa génération", "context size(tokens)": "<PERSON><PERSON> du <PERSON>e (en tokens)", "unlocked": "Déver<PERSON><PERSON><PERSON>", "Only enable this if your model supports context sizes greater than 8192 tokens": "Activez cela uniquement si votre modèle prend en charge des tailles de contexte supérieures à 8192 tokens", "Max prompt cost:": "Coût maximum du prompt:", "Display the response bit by bit as it is generated.": "Afficher la réponse bit par bit au fur et à mesure de sa génération.", "When this is off, responses will be displayed all at once when they are complete.": "Lorsque cette fonction est désactivée, les réponses s'affichent toutes en une fois lorsqu'elles sont complètes.", "Temperature": "Température", "rep.pen": "Pénalité de répétition", "Rep. Pen. Range.": "Plage de pénalité de répétition", "Rep. Pen. Slope": "Pente de la pénalité de répétition", "Rep. Pen. Freq.": "Fréquence de pénalité de répétition", "Rep. Pen. Presence": "Présence de pénalité de répétition", "TFS": "TFS", "Phrase Repetition Penalty": "Pénalité de répétition de phrase", "Off": "Désactivé", "Very light": "<PERSON><PERSON><PERSON>", "Light": "<PERSON><PERSON><PERSON>", "Medium": "<PERSON><PERSON><PERSON>", "Aggressive": "Agress<PERSON>", "Very aggressive": "<PERSON><PERSON><PERSON> a<PERSON>", "Unlocked Context Size": "<PERSON>lle du contexte déverrou<PERSON>", "Unrestricted maximum value for the context slider": "Valeur maximale non limitée pour le curseur de contexte", "Context Size (tokens)": "<PERSON><PERSON> (tokens)", "Max Response Length (tokens)": "Longueur maximale de la réponse (tokens)", "Multiple swipes per generation": "Plusieurs balayages par génération", "Enable OpenAI completion streaming": "Activer le streaming de complétion OpenAI", "Frequency Penalty": "Pénalité de fréquence", "Presence Penalty": "Pénalité de présence", "Top K": "Top K", "Top P": "Top P", "Repetition Penalty": "Pénalité de répétition", "Min P": "<PERSON>", "Top A": "Top A", "Quick Prompts Edit": "Édition rapide des prompts", "Main": "Principal", "Utility Prompts": "Prompts utilitaires", "Impersonation prompt": "Prompt d'usurpation", "Restore default prompt": "Restaurer le prompt par défaut", "Prompt that is used for Impersonation function": "Prompt utilisée pour la fonction d'usurpation", "World Info Format Template": "Modèle de format des World Info", "Restore default format": "Restaurer le format par défaut", "Wraps activated World Info entries before inserting into the prompt.": "Encapsule les entrées World Info activées avant de les insérer dans le prompt.", "scenario_format_template_part_1": "Utiliser", "scenario_format_template_part_2": "pour marquer un endroit où le contenu est inséré.", "Scenario Format Template": "Modèle de format de scénario", "Personality Format Template": "Modèle de format de personnalité", "Group Nudge Prompt Template": "<PERSON>d<PERSON><PERSON> de prompt de groupe Nudge", "Sent at the end of the group chat history to force reply from a specific character.": "Envoyé à la fin de l'historique des discussions de groupe pour forcer la réponse d'un personnage spécifique.", "New Chat": "Nouvelle discussion", "Restore new chat prompt": "Restaurer le prompt de nouvelle discussion", "Set at the beginning of the chat history to indicate that a new chat is about to start.": "Placez-le au début de l'historique de discussion pour indiquer qu'une nouvelle discussion est sur le point de démarrer.", "New Group Chat": "Nouvelle discussion de groupe", "Restore new group chat prompt": "Restaurer le prompt pour les nouvelle discussion de groupe", "Set at the beginning of the chat history to indicate that a new group chat is about to start.": "Défini au début de l'historique des discussions pour indiquer qu'une nouvelle discussion de groupe est sur le point de démarrer.", "New Example Chat": "Nouvel exemple de discussion", "Set at the beginning of Dialogue examples to indicate that a new example chat is about to start.": "Défini au début des exemples de dialogue pour indiquer qu'un nouvel exemple est sur le point de démarrer.", "Continue nudge": "Continuer le coup de pouce", "Set at the end of the chat history when the continue button is pressed.": "Défini à la fin de l’historique des discussions lorsque vous appuyez sur le bouton Continuer.", "Replace empty message": "Remplacer le message vide", "Send this text instead of nothing when the text box is empty.": "Envoyer ce texte à la place de rien lorsque la zone de texte est vide.", "Seed": "Graine", "Set to get deterministic results. Use -1 for random seed.": "Réglé pour obtenir des résultats déterministes. Utilisez -1 pour les graines aléatoires.", "Temperature controls the randomness in token selection": "La température contrôle l'aléatoire dans la sélection des tokens:\n- Une température basse (<1.0) entraîne un texte plus prévisible, en donnant la priorité aux tokens à forte probabilité.\n- Une température élevée (>1.0) favorise la créativité et la diversité, en donnant plus de chances aux tokens à faible probabilité.\nRéglez la valeur à 1.0 pour les probabilités d'origine.", "Top_K_desc": "Top K définit une quantité maximale de tokens les plus fréquents qui peuvent être sélectionnés.", "Top_P_desc": "Top P (alias échantillonnage du noyau) regroupe tous les tokens supérieurs nécessaires pour atteindre un pourcentage spécifique.\nAutrement dit, si les deux premiers tokens représentent 25 % et que Top-P est de 0,50, seuls ces deux tokens sont considérés.\nRéglez la valeur à 1.0 pour la désactiver.", "Typical P": "P Typique", "Typical_P_desc": "L'échantillonnage P Typique privilégie les tokens en fonction de leur écart par rapport à l'entropie moyenne de l'ensemble.\nLes tokens dont la probabilité cumulée est proche du seuil spécifié (par exemple, 0.5) sont conservés, ce qui distingue ceux contenant une information moyenne.\nRéglez la valeur à 1.0 pour la désactiver.", "Min_P_desc": "Min P définit une probabilité minimale de base. Elle est optimisée en fonction de la probabilité du token supérieur.\nSi la probabilité du token supérieur est de 80 % et que Min P est de 0.1, seuls les tokens avec une probabilité supérieure à 8 % sont considérés.\nRéglez la valeur à 0 pour la désactiver.", "Top_A_desc": "Top A définit un seuil pour la sélection des tokens en fonction du carré de la probabilité du token le plus élevé.\nSi Top A est de 0.2 et que la probabilité du token le plus élevé est de 50 %, les tokens avec une probabilité inférieure à 5 % sont exclus (0.2 * 0.5^2).\nRéglez la valeur à 0 pour la désactiver.", "Tail_Free_Sampling_desc": "Échantillonnage sans queue (TFS) recherche les tokens de queue ayant une faible probabilité dans la distribution,\n en analysant le taux de changement des probabilités des tokens à l'aide de dérivées. Les tokens sont conservés jusqu'au seuil (par exemple, 0.3), en fonction de la dérivée seconde uniforme.\nPlus la valeur se rapproche de 0, plus le nombre de tokens rejetés augmente. Réglez la valeur à 1.0 pour la désactiver.", "rep.pen range": "Plage de pénalité de répétition", "Mirostat": "Mirostat", "Mode": "Mode", "Mirostat_Mode_desc": "Une valeur de 0 désactive complètement Mirostat. 1 est pour Mirostat 1.0 et 2 est pour Mirostat 2.0", "Tau": "Tau", "Mirostat_Tau_desc": "Contrôle la variabilité des sorties Mirostat", "Eta": "Eta", "Mirostat_Eta_desc": "Contrôle le taux d’apprentissage du Mirostat", "Ban EOS Token": "Interdire le token EOS", "Ban_EOS_Token_desc": "Interdisez le jeton de fin de séquence (EOS) avec KoboldCpp (et éventuellement aussi d'autres jetons avec KoboldAI).\rIdéal pour l'écriture d'histoires, mais ne doit pas être utilisé pour le mode chat et instruction.", "GBNF Grammar": "Grammaire GBNF", "Type in the desired custom grammar": "Saisissez la grammaire personnalisée souhaitée", "Samplers Order": "Ordre des échantillonneurs", "Samplers will be applied in a top-down order. Use with caution.": "Les échantillonneurs seront appliqués dans un ordre de haut en bas. Utilisez avec prudence.", "Tail Free Sampling": "Échantillonnage sans queue", "Load koboldcpp order": "Charger l'ordre koboldcpp", "Preamble": "Préambule", "Use style tags to modify the writing style of the output.": "Utilisez des balises de style pour modifier le style d'écriture de la sortie.", "Banned Tokens": "Tokens interdits", "Sequences you don't want to appear in the output. One per line.": "Séquences que vous ne voulez pas voir apparaître dans la sortie. Une par ligne.", "Logit Bias": "Biais de logit", "Add": "Ajouter", "Helps to ban or reenforce the usage of certain words": "Aide à interdire ou à renforcer l'utilisation de certains mots", "CFG Scale": "Échelle CFG", "Negative Prompt": "Prompt négatif", "Add text here that would make the AI generate things you don't want in your outputs.": "Ajou<PERSON>z ici du texte qui ferait générer à l'IA des choses que vous ne voulez pas dans vos sorties.", "Used if CFG Scale is unset globally, per chat or character": "Utilisé si l'échelle CFG n'est pas définie globalement, par chat ou par personnage.", "Mirostat Tau": "Tau Mirostat", "Mirostat LR": "Mirostat LR", "Min Length": "Longueur minimale", "Top K Sampling": "Échantillonnage Top K", "Nucleus Sampling": "Échantillonnage du noyau", "Top A Sampling": "Échantillonnage Top A", "CFG": "CFG", "Neutralize Samplers": "Neutraliser les échantillonneurs", "Set all samplers to their neutral/disabled state.": "Définir tous les échantillonneurs sur leur état neutre/désactivé.", "Sampler Select": "Sélection de l'échantillonneur", "Customize displayed samplers or add custom samplers.": "Personnalisez les échantillonneurs affichés ou ajoutez des échantillonneurs personnalisés.", "Epsilon Cutoff": "Coupure epsilon", "Epsilon cutoff sets a probability floor below which tokens are excluded from being sampled": "La coupure epsilon définit un seuil de probabilité en dessous duquel les tokens sont exclus de l'échantillonnage.\nEn unités 1e-4; la valeur appropriée est 3. Réglez-la à 0 pour la désactiver.", "Eta Cutoff": "Coupure Eta", "Eta_Cutoff_desc": "Le seuil Eta est le principal paramètre de la technique d'échantillonnage Eta spéciale.&#13;En unités de 1e-4 ; une valeur raisonnable est 3.&#13;Réglez sur 0 pour désactiver.&#13;Voir l'article Truncation Sampling as Language Model Desmoothing par <PERSON> et al. (2022) pour plus de détails.", "rep.pen decay": "Déclin de la pénalité de répétition", "Encoder Rep. Pen.": "Pénalité de répétition de l'encodeur", "No Repeat Ngram Size": "Taille de n-gramme sans répétition", "Skew": "<PERSON><PERSON><PERSON>", "Max Tokens Second": "Nombre maximum de token par seconde", "Smooth Sampling": "Échantillonnage fluide", "Smooth_Sampling_desc": "Vous permet d'utiliser des transformations quadratiques/cubiques pour ajuster la distribution. Les valeurs inférieures du facteur de lissage seront plus créatives, généralement entre 0,2 et 0,3 comme point idéal (en supposant que la courbe = 1). Des valeurs de courbe de lissage plus élevées rendront la courbe plus raide, ce qui punira plus agressivement les choix à faible probabilité. La courbe 1,0 équivaut à utiliser uniquement le facteur de lissage.", "Smoothing Factor": "Facteur de lissage", "Smoothing Curve": "Courbe de lissage", "DRY_Repetition_Penalty_desc": "DRY pénalise les jetons qui prolongeraient la fin de l'entrée dans une séquence qui s'est déjà produite dans l'entrée. Réglez le multiplicateur sur 0 pour le désactiver.", "DRY Repetition Penalty": "Pénalité de répétition SÈCHE", "DRY_Multiplier_desc": "Réglez sur une valeur > 0 pour activer DRY. Contrôle l'ampleur de la pénalité pour les séquences pénalisées les plus courtes.", "Multiplier": "Multiplicateur", "DRY_Base_desc": "Contrôle la vitesse à laquelle la pénalité augmente avec la longueur de la séquence.", "Base": "Base", "DRY_Allowed_Length_desc": "Séquence la plus longue pouvant être répétée sans être pénalisée.", "Allowed Length": "Longueur autorisée", "Penalty Range": "Fourchette de pénalité", "DRY_Sequence_Breakers_desc": "Jetons sur lesquels la correspondance de séquence n'est pas poursuivie. Spécifié sous la forme d'une liste de chaînes entre guillemets séparées par des virgules.", "Sequence Breakers": "Interrupteurs de séquence", "JSON-serialized array of strings.": "Tableau de chaînes sérialisées JSON.", "Dynamic Temperature": "Température dynamique", "Scale Temperature dynamically per token, based on the variation of probabilities": "Échelonnez dynamiquement la température par token, en fonction de la variation des probabilités.", "Minimum Temp": "Température minimale", "Maximum Temp": "Température maximale", "Exponent": "Exposant", "Mirostat (mode=1 is only for llama.cpp)": "Mirostat (le mode=1 est uniquement pour llama.cpp)", "Mirostat_desc": "Mirostat est un thermostat pour la perplexité de sortie", "Mirostat Mode": "Mode Mirostat", "Variability parameter for Mirostat outputs": "Paramètre de variabilité pour les sorties Mirostat.", "Mirostat Eta": "Eta Mirostat", "Learning rate of Mirostat": "Taux d'apprentissage de Mirostat.", "Beam search": "Recherche par faisceau", "Length Penalty": "Pénalité de longueur", "Early Stopping": "<PERSON><PERSON><PERSON><PERSON>", "Contrastive search": "Recherche contrastive", "Penalty Alpha": "Alpha de pénalité", "Strength of the Contrastive Search regularization term. Set to 0 to disable CS": "Force du terme de régularisation de la recherche contrastive. Réglez la valeur à 0 pour la désactiver.", "Do Sample": "Faire une échantillon", "Add BOS Token": "Ajouter le token BOS", "Add the bos_token to the beginning of prompts. Disabling this can make the replies more creative": "A<PERSON><PERSON>z le token BOS au début des prompts. Désactiver cela peut rendre les réponses plus créatives", "Ban the eos_token. This forces the model to never end the generation prematurely": "Interdire le token EOS. Cela force le modèle à ne jamais terminer la génération prématurément", "Ignore EOS Token": "Ignorer le token EOS", "Ignore the EOS Token even if it generates.": "Ignorez le token EOS même s'il est généré.", "Skip Special Tokens": "Ignorer les tokens spéciaux", "Temperature Last": "Température en dernier", "Temperature_Last_desc": "Utilisez le réglage de température en dernier. Cela est généralement logique.\nLorsqu'il est activé : une sélection de tokens potentiels est d'abord effectuée, puis la température est appliquée pour corriger leurs probabilités relatives (techniquement, les log-likelihoods).\nLorsqu'il est désactivé : la température est d'abord appliquée pour corriger les probabilités relatives de tous les tokens, puis une sélection de tokens potentiels est effectuée parmi eux.\nDésactivez la température en dernier.", "Speculative Ngram": "<PERSON><PERSON><PERSON> s<PERSON>", "Use a different speculative decoding method without a draft model": "Utilisez une méthode de décodage spéculative différente sans projet de modèle.\rIl est préférable d’utiliser une ébauche de modèle. Le ngram spéculatif n’est pas aussi efficace.", "Spaces Between Special Tokens": "Espaces entre les tokens spéciaux", "LLaMA / Mistral / Yi models only": "Modèles LLaMA / Mistral / Yi uniquement. Assurez-vous de sélectionner d'abord l'analyste approprié.\nLes chaînes de caractères ne doivent pas apparaître dans les résultats.\nUne chaîne par ligne. Texte ou [identifiants de tokens].\nDe nombreux tokens commencent par un espace. Utilisez un compteur de tokens si vous n'êtes pas sûr.", "Example: some text [42, 69, 1337]": "Exemple:\nun texte\n[42, 69, 1337]", "Classifier Free Guidance. More helpful tip coming soon": "Guidance gratuite du classificateur. Des conseils plus utiles arrivent bi<PERSON><PERSON><PERSON>.", "Scale": "<PERSON><PERSON><PERSON>", "JSON Schema": "Schéma JSON", "Type in the desired JSON schema": "Ta<PERSON>z le schéma JSON souhaité", "Grammar String": "<PERSON><PERSON><PERSON> grammaire", "GBNF or EBNF, depends on the backend in use. If you're using this you should know which.": "GBNF ou EBNF dépend du backend utilisé. Si vous l'utilisez, vous devez savoir lequel.", "Top P & Min P": "Top P et Min P", "Load default order": "Charger l'ordre par défaut", "llama.cpp only. Determines the order of samplers. If Mirostat mode is not 0, sampler order is ignored.": "lama.cpp uniquement. Détermine l’ordre des échantillonneurs. Si le mode Mirostat n'est pas à 0, l'ordre de l'échantillonneur est ignoré.", "Sampler Priority": "Priorité de l'échantillonneur", "Ooba only. Determines the order of samplers.": "Ooba uniquement. Détermine l'ordre des échantillonneurs.", "Character Names Behavior": "Comportement des noms de personnages", "Helps the model to associate messages with characters.": "Aide le modèle à associer les messages avec les personnages.", "None": "Aucun", "character_names_default": "Sauf pour les groupes et les personnages passés. Sinon, assurez-vous de fournir des noms dans le prompt.", "character_names_completion": "Des restrictions s'appliquent : uniquement les caractères alphanumériques latins et les traits de soulignement. Ne fonctionne pas pour toutes les sources, notamment : Claude, MistralAI, Google.", "Message Content": "Contenu du message", "Prepend character names to message contents.": "<PERSON><PERSON><PERSON>z les noms de caractères au contenu du message.", "Continue Postfix": "<PERSON><PERSON><PERSON> Postfix", "The next chunk of the continued message will be appended using this as a separator.": "Le prochain morceau du message continu sera ajouté en utilisant celui-ci comme séparateur.", "Space": "Espace", "Newline": "Nouvelle ligne", "Double Newline": "Double nouvelle ligne", "Wrap user messages in quotes before sending": "Envelopper les messages de l'utilisateur entre guillemets avant de les envoyer", "Wrap in Quotes": "Envelopper entre guillemets", "Wrap entire user message in quotes before sending.": "Envelopper l'ensemble du message utilisateur entre guillemets avant de l'envoyer.", "Leave off if you use quotes manually for speech.": "Laissez-le en dehors si vous utilisez manuellement des guillemets pour la parole.", "Continue prefill": "Con<PERSON>uer le pré-remplissage", "Continue sends the last message as assistant role instead of system message with instruction.": "Continuer envoie le dernier message en tant que rôle d'assistant au lieu d'un message système avec instruction.", "Squash system messages": "Combiner les messages système", "Combines consecutive system messages into one (excluding example dialogues). May improve coherence for some models.": "Combine les messages système consécutifs en un seul (à l'exclusion des dialogues d'exemple). Peut améliorer la cohérence pour certains modèles.", "Enable function calling": "Activer l'appel de fonction", "Send inline images": "Envoyer des images en ligne", "image_inlining_hint_1": "Envoie des images dans les prompts si le modèle le prend en charge.\nUtilisez le", "image_inlining_hint_2": "action sur n'importe quel message ou le", "image_inlining_hint_3": "menu pour joindre un fichier image au chat.", "Inline Image Quality": "Qualité d'image en ligne", "openai_inline_image_quality_auto": "Auto", "openai_inline_image_quality_low": "Faible", "openai_inline_image_quality_high": "<PERSON><PERSON>", "Use system prompt": "Utiliser le prompt système", "Merges_all_system_messages_desc_1": "Fusionne tous les messages système jusqu'au premier message avec un rôle non-système et les envoie dans un", "Merges_all_system_messages_desc_2": "champ.", "Assistant Prefill": "Pré-remplissage de l'assistant", "Start Claude's answer with...": "Commencer la réponse de Claude par...", "Assistant Impersonation Prefill": "<PERSON>ré-remplir l'usurpation d'identité de l'assistant", "Send the system prompt for supported models. If disabled, the user message is added to the beginning of the prompt.": "Envoyer le prompt système pour les modèles pris en charge. Si désactivé, le message de l'utilisateur est ajouté au début du prompt.", "New preset": "Nouveau preset", "Delete preset": "Supprimer le preset", "View / Edit bias preset": "Afficher/Modifier le preset de biais", "Add bias entry": "Ajouter une entrée de biais", "Most tokens have a leading space.": "La plupart des tokens sont précédés d'un espace.", "API Connections": "Connexions API", "Text Completion": "Complétion de texte", "Chat Completion": "Complétion de chat", "NovelAI": "NovelAI", "AI Horde": "AI Horde", "KoboldAI": "KoboldAI", "Avoid sending sensitive information to the Horde.": "<PERSON><PERSON><PERSON><PERSON> d'envoyer des informations sensibles à la Horde.", "Review the Privacy statement": "Examiner la déclaration de confidentialité", "Register a Horde account for faster queue times": "Enregistrez un compte Horde pour accélérer les files d'attente", "Learn how to contribute your idle GPU cycles to the Horde": "Découvrez comment contribuer à la Horde avec vos cycles GPU inactifs.", "Adjust context size to worker capabilities": "Ajuster la taille du contexte aux capacités des travailleurs", "Adjust response length to worker capabilities": "Ajuster la longueur de la réponse aux capacités des travailleurs", "Can help with bad responses by queueing only the approved workers. May slowdown the response time.": "Peut aider avec les mauvaises réponses en mettant en file d'attente uniquement les travailleurs approuvés. Peut ralentir le temps de réponse.", "Trusted workers only": "Travailleurs de confiance uniquement", "API key": "Clé API", "Get it here:": "Obtenez-la ici :", "Register": "S'inscrire", "View my Kudos": "Voir mes Kudos", "Enter": "Entrez", "to use anonymous mode.": "pour utiliser le mode anonyme.", "Clear your API key": "Effacer votre clé API", "For privacy reasons, your API key will be hidden after you reload the page.": "Pour des raisons de confidentialité, votre clé API sera masquée après le rechargement de la page.", "Models": "<PERSON><PERSON><PERSON><PERSON>", "Refresh models": "Actualiser les modèles", "-- Horde models not loaded --": "-- <PERSON><PERSON><PERSON><PERSON> non chargés --", "Not connected...": "Non connecté...", "API url": "URL de l'API", "Example: http://127.0.0.1:5000/api ": "Exemple : http://127.0.0.1:5000/api", "Connect": "Connection", "Cancel": "Annuler", "Novel API key": "Clé API de NovelAI", "Get your NovelAI API Key": "Obtenez votre clé API NovelAI", "Enter it in the box below": "Entrez-le dans la boîte ci-dessous", "Novel AI Model": "Modèle NovelAI", "No connection...": "Pas de connexion...", "API Type": "Type d'API", "TogetherAI API Key": "Clé API de TogetherAI", "TogetherAI Model": "Modèle TogetherAI", "-- Connect to the API --": "-- Se connecter à l'API --", "OpenRouter API Key": "Clé API OpenRouter", "Click Authorize below or get the key from": "Cliquez sur Autoriser ci-dessous ou obtenez la clé à partir de", "View Remaining Credits": "Afficher les crédits restants", "OpenRouter Model": "<PERSON><PERSON><PERSON><PERSON>", "Model Providers": "Fournisseurs de modèles", "InfermaticAI API Key": "Clé API InfermaticAI", "InfermaticAI Model": "Modèle InfermaticAI", "DreamGen API key": "Clé API DreamGen", "DreamGen Model": "<PERSON><PERSON><PERSON><PERSON>en", "Mancer API key": "Clé API de Mancer", "Mancer Model": "<PERSON><PERSON><PERSON><PERSON>", "Make sure you run it with": "Assurez-vous de l'exécuter avec", "flag": "fanion", "API key (optional)": "Clé API (optionnelle)", "Server url": "URL du serveur", "Example: http://127.0.0.1:5000": "Exemple : http://127.0.0.1:5000", "Custom model (optional)": "<PERSON><PERSON><PERSON><PERSON> (optionnel)", "vllm-project/vllm": "vllm-project/vllm (mode wrapper de l'API OpenAI)", "vLLM API key": "Clé API vLLM", "Example: http://127.0.0.1:8000": "Exemple : http://127.0.0.1:8000", "vLLM Model": "Modèle vLLM", "PygmalionAI/aphrodite-engine": "PygmalionAI/aphrodite-engine (mode wrapper pour l'API OpenAI)", "Aphrodite API key": "Clé API Aphrodite", "Aphrodite Model": "<PERSON><PERSON><PERSON><PERSON>", "ggerganov/llama.cpp": "ggerganov/llama.cpp", "Example: http://127.0.0.1:8080": "Exemple : http://127.0.0.1:8080", "Example: http://127.0.0.1:11434": "Exemple : http://127.0.0.1:11434", "Ollama Model": "<PERSON><PERSON><PERSON><PERSON>", "Download": "Télécharger", "Tabby API key": "Clé API de Tabby", "koboldcpp API key (optional)": "Clé API koboldcpp (facultatif)", "Example: http://127.0.0.1:5001": "Exemple : http://127.0.0.1:5001", "Authorize": "Autoriser", "Get your OpenRouter API token using OAuth flow. You will be redirected to openrouter.ai": "Obtenez votre jeton API OpenRouter en utilisant le flux OAuth. Vous serez redirigé vers openrouter.ai", "Bypass status check": "Contourner la vérification de l'état", "Chat Completion Source": "Source de complétion de chat", "Reverse Proxy": "Reverse Proxy", "Proxy Presets": "Presets proxy", "Saved addresses and passwords.": "Adresses et mots de passe enregistrés.", "Save Proxy": "Enregistrer le proxy", "Delete Proxy": "Supprimer le proxy", "Proxy Name": "Nom du proxy", "This will show up as your saved preset.": "<PERSON><PERSON> apparaîtra comme votre preset enregistré.", "Proxy Server URL": "URL du serveur proxy", "Alternative server URL (leave empty to use the default value).": "URL du serveur alternatif (laissez vide pour utiliser la valeur par défaut).", "Doesn't work? Try adding": "Ça ne marche pas ? Essayez d'ajouter", "at the end!": "à la fin!", "Proxy Password": "Mot de passe proxy", "Will be used as a password for the proxy instead of API key.": "Sera utilisé comme mot de passe pour le proxy au lieu de la clé API.", "Peek a password": "Jetez un œil à un mot de passe", "OpenAI API key": "Clé API OpenAI", "View API Usage Metrics": "Afficher les statistiques d'utilisation de l'API", "Follow": "Suivre", "these directions": "ces instructions", "to get your OpenAI API key.": "pour obtenir votre clé API OpenAI.", "Use Proxy password field instead. This input will be ignored.": "Util<PERSON><PERSON> plut<PERSON>t le champ « Mot de passe proxy ». Cette entrée sera ignorée.", "OpenAI Model": "Modèle OpenAI", "Bypass API status check": "Contourner la vérification de l'état de l'API", "Show External models (provided by API)": "Afficher les modèles externes (fournis par l'API)", "Get your key from": "Obtenez votre clé à partir de", "Anthropic's developer console": "<PERSON><PERSON><PERSON> de d<PERSON>veloppement d'Anthropic", "Claude Model": "<PERSON><PERSON><PERSON><PERSON>", "Window AI Model": "Modèle Window AI", "Model Order": "Tri des modèles OpenRouter", "Alphabetically": "Alphabétiquement", "Price": "Prix ​​(le moins cher)", "Context Size": "<PERSON><PERSON>", "Group by vendors": "Regrouper par fournisseurs", "Group by vendors Description": "Placez les modèles OpenAI dans un groupe, les modèles Anthropic dans un autre groupe, etc. Peut être combiné avec le tri.", "Allow fallback routes Description": "Le modèle alternatif est automatiquement sélectionné si le modèle choisi ne peut pas répondre à votre demande.", "AI21 API Key": "Clé API AI21", "AI21 Model": "Modèle AI21", "Google AI Studio API Key": "Clé API Google AI Studio", "Google Model": "Modèle Google", "MistralAI API Key": "Clé API MistralAI", "MistralAI Model": "<PERSON><PERSON><PERSON><PERSON> Mi<PERSON>lAI", "Groq API Key": "Clé API Groq", "Groq Model": "<PERSON><PERSON><PERSON><PERSON>", "Perplexity API Key": "Clé API Perplexity", "Perplexity Model": "Mod<PERSON>le Perplexity", "Cohere API Key": "Clé API Cohere", "Cohere Model": "<PERSON><PERSON><PERSON><PERSON>", "Custom Endpoint (Base URL)": "Point de terminaison personnalisé (URL de base)", "Custom API Key": "Clé API personnalisée", "Available Models": "<PERSON><PERSON><PERSON><PERSON> disponi<PERSON>", "Prompt Post-Processing": "Post-traitement de prompt", "Applies additional processing to the prompt before sending it to the API.": "Applique un traitement supplémentaire au prompt avant de l'envoyer à l'API.", "Verifies your API connection by sending a short test message. Be aware that you'll be credited for it!": "Vérifie votre connexion API en envoyant un court message de test. Soyez conscient que vous serez crédité pour cela !", "Test Message": "Message de test", "Auto-connect to Last Server": "Connexion automatique au dernier serveur", "Missing key": "❌ <PERSON><PERSON> manquante", "Key saved": "✔️ Clé enregistrée", "View hidden API keys": "Afficher les clés API cachées", "AI Response Formatting": "Formatage de la réponse de l'IA", "Advanced Formatting": "Formatage avancé", "Context Template": "<PERSON><PERSON><PERSON><PERSON>", "Story String": "Chaîne d'histoire", "Example Separator": "Séparateur d'exemple", "Chat Start": "Début de <PERSON>", "Add Chat Start and Example Separator to a list of stopping strings.": "<PERSON><PERSON><PERSON><PERSON> Start et Sample Separator à une liste de chaînes d’arrê<PERSON>.", "Author's Note": "Note d'auteur", "Enabled": "Activé", "instruct_bind_to_context": "Si cette option est activée, les modèles de contexte seront automatiquement sélectionnés en fonction du nom du modèle d'instruction sélectionné ou par préférence.", "Activation Regex": "Regex d'activation", "Wrap Sequences with Newline": "Envelopper les séquences avec un saut de ligne", "Replace Macro in Sequences": "Remplacer la macro dans les séquences", "Skip Example Dialogues Formatting": "Ignorer le formatage des dialogues d'exemple", "Include Names": "Inclure les noms", "System Prompt": "Prompt système", "Inserted before a System prompt.": "Inséré avant un prompt système.", "System Prompt Prefix": "Préfixe de prompt système", "Inserted after a System prompt.": "Inséré après un prompt système.", "System Prompt Suffix": "Suffixe de prompt système", "Inserted before a User message and as a last prompt line when impersonating.": "Inséré avant un message utilisateur et comme dernière ligne du prompt lors de l'usurpation d'identité.", "Inserted after a User message.": "Inséré après un message utilisateur.", "Inserted before an Assistant message and as a last prompt line when generating an AI reply.": "Inséré avant un message de l'Assistant et comme dernière ligne du prompt lors de la génération d'une réponse de l'IA.", "Inserted after an Assistant message.": "Ins<PERSON><PERSON> après un message de l'assistant.", "Inserted before a System (added by slash commands or extensions) message.": "Inséré avant un message système (ajouté par des commandes slash ou des extensions).", "Inserted after a System message.": "Inséré après un message système.", "If enabled, System Sequences will be the same as User Sequences.": "Si cette option est activée, les séquences système seront les mêmes que les séquences utilisateur.", "System same as User": "Système identique à l'utilisateur", "Misc. Sequences": "Séquences diverses", "Inserted before the first Assistant's message.": "Insé<PERSON> avant le premier message de l'assistant.", "First Assistant Prefix": "Préfixe du premier assistant", "instruct_last_output_sequence": "Inséré avant le dernier message de l'assistant ou comme dernière ligne du prompt lors de la génération d'une réponse de l'IA (sauf un rôle neutre/système).", "Last Assistant Prefix": "<PERSON><PERSON><PERSON><PERSON> du dernier assistant", "Will be inserted as a last prompt line when using system/neutral generation.": "Sera inséré comme dernière ligne du prompt lors de l’utilisation de la génération système/neutre.", "System Instruction Prefix": "Préfixe d'instruction système", "If a stop sequence is generated, everything past it will be removed from the output (inclusive).": "Si une séquence d'arrêt est générée, tout ce qui se trouve au-delà sera supprimé de la sortie (inclus).", "Stop Sequence": "<PERSON><PERSON><PERSON>", "Will be inserted at the start of the chat history if it doesn't start with a User message.": "Sera inséré au début de l'historique des discussions s'il ne commence pas par un message utilisateur.", "User Filler Message": "Message de remplissage de l'utilisateur", "Context Formatting": "Formatage du contexte", "Always add character's name to prompt": "Toujours ajouter le nom du personnage au prompt", "Generate only one line per request": "<PERSON><PERSON><PERSON><PERSON> seulement une ligne par requête", "Trim Incomplete Sentences": "Supprimer les phrases incomplètes", "Collapse Consecutive Newlines": "Réduire les sauts de ligne consécutifs", "Trim spaces": "Supprimer les espaces", "Tokenizer": "Tokeniseur", "Token Padding": "Remplissage de token", "Start Reply With": "Commencer la réponse avec", "Show reply prefix in chat": "Affiche<PERSON> le préfixe de réponse dans la conversation", "Non-markdown strings": "Chaînes non Markdown", "Custom Stopping Strings": "Chaînes d'arr<PERSON>t person<PERSON>", "JSON serialized array of strings": "Tableau de chaînes sérialisé JSON", "Replace Macro in Stop Strings": "Remplacer les macro dans les chaînes d'arrêt personnalisées", "Auto-Continue": "Auto-Continue", "Allow for Chat Completion APIs": "Autoriser les APIs de complétion de chat", "Target length (tokens)": "Longueur cible (tokens)", "World Info": "World Info", "Locked = World Editor will stay open": "<PERSON><PERSON><PERSON><PERSON><PERSON> = l'éditeur des World Info restera ouvert", "Worlds/Lorebooks": "Worlds/Lorebooks", "Active World(s) for all chats": "Monde(s) actif(s) pour toutes les conversations", "-- World Info not found --": "-- World Info non trouvées --", "Global World Info/Lorebook activation settings": "Paramètres d'activation globales des World Info/Lorebook", "Click to expand": "Cliquez pour agrandir", "Scan Depth": "Profondeur <PERSON>", "Context %": "Pourcentage de contexte", "Budget Cap": "Limite de budget", "(0 = disabled)": "(0 = désactivé)", "Scan chronologically until reached min entries or token budget.": "Analysez chronologiquement jusqu'à atteindre le nombre minimum d'entrées ou le budget symbolique.", "Min Activations": "Activations minimales", "Max Depth": "Profondeur max", "(0 = unlimited, use budget)": "(0 = illimité, utiliser le budget)", "Insertion Strategy": "Stratégie d'insertion", "Sorted Evenly": "<PERSON>é de manière égale", "Character Lore First": "Lore du personnage d'abord", "Global Lore First": "Lore global d'abord", "Entries can activate other entries by mentioning their keywords": "Les entrées peuvent activer d'autres entrées en mentionnant leurs mots-clés", "Recursive Scan": "Analyse récursive", "Lookup for the entry keys in the context will respect the case": "La recherche des clés d'entrée dans le contexte respectera la casse", "Case Sensitive": "Sensible à la casse", "If the entry key consists of only one word, it would not be matched as part of other words": "Si la clé de l'entrée se compose d'un seul mot, elle ne sera pas considérée comme faisant partie d'autres mots", "Match Whole Words": "Correspondre aux mots entiers", "Only the entries with the most number of key matches will be selected for Inclusion Group filtering": "Seules les entrées avec le plus grand nombre de correspondances clés seront sélectionnées pour le filtrage des groupes d'inclusion.", "Use Group Scoring": "Utiliser la notation de groupe", "Alert if your world info is greater than the allocated budget.": "Alertez si vos informations mondiales sont supérieures au budget alloué.", "Alert On Overflow": "Alerte en cas de dépassement", "New": "Nouveau", "or": "ou", "--- Pick to Edit ---": "--- Sélectionnez pour éditer ---", "Rename World Info": "Renommer le World Info", "Open all Entries": "<PERSON><PERSON><PERSON><PERSON><PERSON> toutes les entrées", "Close all Entries": "<PERSON><PERSON><PERSON> toutes les entrées", "New Entry": "Nouvelle entrée", "Fill empty Memo/Titles with Keywords": "Remp<PERSON>r les mémos/titres vides avec des mots-clés", "Import World Info": "Importer un Wolrd Info", "Export World Info": "Exporter le Wolrd Info", "Duplicate World Info": "Dupliquer le World Info", "Delete World Info": "Supprimer le World Info", "Search...": "Rechercher...", "Search": "Recherche", "Priority": "Priorité", "Custom": "<PERSON><PERSON><PERSON><PERSON>", "Title A-Z": "Titre A-Z", "Title Z-A": "Titre Z-A", "Tokens ↗": "Tokens ↗", "Tokens ↘": "Tokens ↘", "Depth ↗": "Profondeur ↗", "Depth ↘": "Profondeur ↘", "Order ↗": "<PERSON><PERSON> ↗", "Order ↘": "<PERSON><PERSON> ↘", "UID ↗": "UID ↗", "UID ↘": "UID ↘", "Trigger% ↗": "Déclencheur% ↗", "Trigger% ↘": "Déclencheur% ↘", "Refresh": "Actualiser", "User Settings": "Paramètres utilisateur", "UI Language": "<PERSON><PERSON>", "Account": "<PERSON><PERSON><PERSON>", "Admin Panel": "Panneau d'administration", "Logout": "Déconnection", "Search Settings": "Recherche de paramètres", "UI Theme": "Thème de l'UI", "Import a theme file": "Importer un fichier de thème", "Export a theme file": "Exporter un fichier de thème", "Delete a theme": "Supprimer un thème", "Update a theme file": "Mettre à jour un fichier de thème", "Save as a new theme": "Enregistrer en tant que nouveau thème", "Avatar Style:": "Style d'avatar", "Circle": "Cercle", "Square": "Carré", "Rectangle": "Rectangle", "Chat Style:": "Style de la discussion :", "Flat": "Plat\nBulles\nDocument", "Bubbles": "<PERSON><PERSON>", "Document": "Document", "Specify colors for your theme.": "Spécifiez les couleurs de votre thème.", "Theme Colors": "Couleurs du thème", "Main Text": "Texte principal", "Italics Text": "Texte en italique", "Underlined Text": "<PERSON><PERSON>", "Quote Text": "Texte de citation", "Shadow Color": "Couleur de l'ombre", "Chat Background": "Arrière-plan de la discussion", "UI Background": "Arrière-plan de l'interface utilisateur", "UI Border": "Bordure de l'interface utilisateur", "User Message Blur Tint": "Teinte de flou du message utilisateur", "AI Message Blur Tint": "Teinte de flou du message AI", "Chat Width": "Largeur de <PERSON>", "Width of the main chat window in % of screen width": "Largeur de la fenêtre de discussion principale en % de la largeur de l'écran", "Font Scale": "Échelle de police", "Font size": "Taille de police", "Blur Strength": "Force du flou", "Blur strength on UI panels.": "Force du flou sur les panneaux de l'interface utilisateur.", "Text Shadow Width": "Largeur de l'ombre du texte", "Strength of the text shadows": "Force des ombres du texte", "Disables animations and transitions": "Désactive les animations et transitions", "Reduced Motion": "Mouvement réduit", "removes blur from window backgrounds": "Supprime le flou des arrière-plans de fenêtre", "No Blur Effect": "<PERSON><PERSON> d'effet de flou", "Remove text shadow effect": "Supprimer l'effet d'ombre du texte", "No Text Shadows": "Pas d'ombres de texte", "Reduce chat height, and put a static sprite behind the chat window": "Réduire la hauteur de la discussion et placer un sprite statique derrière la fenêtre de discussion", "Waifu Mode": "Mode Waifu", "Always show the full list of the Message Actions context items for chat messages, instead of hiding them behind '...'": "Afficher toujours la liste complète des éléments de contexte Actions de message pour les messages de discussion, au lieu de les cacher derrière '...' ", "Auto-Expand Message Actions": "Extension automatique des actions du message", "Alternative UI for numeric sampling parameters with fewer steps": "Interface utilisateur alternative pour les paramètres d'échantillonnage numérique avec moins d'étapes", "Zen Sliders": "Curseurs Zen", "Entirely unrestrict all numeric sampling parameters": "Déverrouiller entièrement tous les paramètres d'échantillonnage numérique", "Mad Lab Mode": "Mode Mad Lab", "Time the AI's message generation, and show the duration in the chat log": "Chronométrer la génération de messages de l'IA et afficher la durée dans le journal de discussion", "Message Timer": "Minuteur de message", "Show a timestamp for each message in the chat log": "Afficher un horodatage pour chaque message dans le journal de discussion", "Chat Timestamps": "Horodatages de la discussion", "Show an icon for the API that generated the message": "Afficher une icône pour l'API qui a généré le message", "Model Icon": "Icône du modèle", "Show sequential message numbers in the chat log": "Afficher les numéros de message séquentiels dans le journal de discussion", "Message IDs": "Identifiants du message", "Hide avatars in chat messages.": "Masquez les avatars dans les messages de discussion.", "Hide Chat Avatars": "Masquer les avatars du chat", "Show the number of tokens in each message in the chat log": "Afficher le nombre de tokens dans chaque message dans le journal de discussion", "Show Message Token Count": "Afficher le nombre de tokens du message", "Single-row message input area. Mobile only, no effect on PC": "Zone de saisie de message sur une seule ligne. Mobile uniquement, aucun effet sur PC", "Compact Input Area (Mobile)": "Zone de saisie compacte (Mobile)", "In the Character Management panel, show quick selection buttons for favorited characters": "Dans le panneau de gestion des personnages, afficher des boutons de sélection rapide pour les personnages favoris", "Characters Hotswap": "Échange rapide de personnages", "Enable magnification for zoomed avatar display.": "Activer le grossissement pour l'affichage de l'avatar zoomé.", "Avatar Hover Magnification": "Agrandissement au survol de l'avatar", "Enables a magnification effect on hover when you display the zoomed avatar after clicking an avatar's image in chat.": "Active un effet de grossissement au survol lorsque vous affichez l'avatar zoomé après avoir cliqué sur l'image d'un avatar dans le chat.", "Show tagged character folders in the character list": "Afficher les dossiers de personnages tagués dans la liste de personnages", "Tags as Folders": "Tags comme dossiers", "Tags_as_Folders_desc": "Modification récente : les tags doivent être marqués comme dossiers dans le menu Gestion des tags pour apparaître comme telles. Cliquez ici pour l'afficher.", "Character Handling": "Gestion des personnages", "If set in the advanced character definitions, this field will be displayed in the characters list.": "Si défini dans les définitions de personnage avancées, ce champ sera affiché dans la liste des personnages.", "Char List Subheader": "Sous-en-tête de la liste des personnages", "Character Version": "Version du personnage", "Created by": "C<PERSON><PERSON> par", "Use fuzzy matching, and search characters in the list by all data fields, not just by a name substring": "Utiliser la correspondance floue et rechercher des personnages dans la liste par tous les champs de données, pas seulement par une sous-chaîne de nom", "Advanced Character Search": "Recherche de personnage avancée", "If checked and the character card contains a prompt override (System Prompt), use that instead": "Si cochée et si la carte de personnage contient un prompt de remplacement (prompt système), l'utiliser à la place", "Prefer Character Card Prompt": "<PERSON><PERSON><PERSON><PERSON><PERSON> le prompt du personnage", "never_resize_avatars_tooltip": "Évitez de recadrer et de redimensionner les images de personnages importés. Lorsqu'il est désactivé, recadrez/redimensionnez à 512 x 768.", "Never resize avatars": "Ne jamais redimensionner les avatars", "Show actual file names on the disk, in the characters list display only": "Afficher les noms de fichier réels sur le disque, dans l'affichage de la liste de personnages uniquement", "Show avatar filenames": "Afficher les noms de fichier des avatars", "Import Card Tags": "Importer les tags de carte", "Hide character definitions from the editor panel behind a spoiler button": "Masquer les définitions de personnages du panneau d'édition derrière un bouton spoiler", "Spoiler Free Mode": "Mode sans spoiler", "Miscellaneous": "Divers", "Reload and redraw the currently open chat": "Recharger et redessiner la discussion actuellement ouverte", "Reload Chat": "Recharger la discussion", "Debug Menu": "Menu de débogage", "Smooth Streaming": "Diffusion fluide", "Experimental feature. May not work for all backends.": "Fonctionnalité expérimentale. Peut ne pas fonctionner pour tous les backends.", "Slow": "<PERSON><PERSON>", "Fast": "Rapide", "Play a sound when a message generation finishes": "Jouer un son lorsque la génération de message est terminée", "Message Sound": "Son de message", "Only play a sound when ST's browser tab is unfocused": "Jouer un son uniquement lorsque l'onglet ST du navigateur n'est pas active", "Background Sound Only": "Son de fond uniquement", "Reduce the formatting requirements on API URLs": "Réduire les exigences de formatage sur les URL d'API", "Relaxed API URLS": "URLs d'API détendues", "Ask to import the World Info/Lorebook for every new character with embedded lorebook. If unchecked, a brief message will be shown instead": "Demander d'importer le Word Info/Lorebook pour chaque nouveau personnage avec Lorebook intégré. Si non cochée, un message bref sera affiché à la place", "Lorebook Import Dialog": "Boîte de dialogue d'importation de Lorebook", "Restore unsaved user input on page refresh": "Restaurer les saisies utilisateur non enregistrées lors du rafraîchissement de la page", "Restore User Input": "Restaurer l'entrée utilisateur", "Allow repositioning certain UI elements by dragging them. PC only, no effect on mobile": "Permettre le repositionnement de certains éléments de l'interface utilisateur en les faisant glisser. PC uniquement, aucun effet sur mobile", "Movable UI Panels": "Panels d'UI déplaçables", "MovingUI preset. Predefined/saved draggable positions": "Preset MovingUI. Positions prédéfinies/enregistrées pouvant être déplacées", "MUI Preset": "Preset MUI", "Save movingUI changes to a new file": "Enregistrer les modifications de MovingUI dans un nouveau fichier", "Reset MovingUI panel sizes/locations.": "Réinitialisez les tailles/emplacements des panneaux MovingUI.", "Apply a custom CSS style to all of the ST GUI": "Appliquer un style CSS personnalisé à toute l'interface utilisateur de ST", "Custom CSS": "CSS personnalisé", "Expand the editor": "Agrandir l'éditeur", "Chat/Message Handling": "Gestion de la discussion/des messages", "# Messages to Load": "# Messages à charger", "The number of chat history messages to load before pagination.": "Le nombre de messages de l'historique des discussions à charger avant la pagination.", "(0 = All)": "(0 = Tout)", "Streaming FPS": "FPS en streaming", "Update speed of streamed text.": "Vitesse de mise à jour du texte diffusé.", "Example Messages Behavior": "Comportement des messages d'exemple", "Gradual push-out": "Poussée progressive", "Always include examples": "Toujours inclure les exemples", "Never include examples": "Ne jamais inclure les exemples", "Send on Enter": "Envoyer sur Entrée", "Disabled": "Désactivé", "Automatic (PC)": "Automatique (PC)", "Press Send to continue": "Appuyez sur Envoyer pour continuer", "Show a button in the input area to ask the AI to continue (extend) its last message": "Afficher un bouton dans la zone de saisie pour demander à l'IA de continuer (étendre) son dernier message", "Quick 'Continue' button": "<PERSON><PERSON><PERSON><PERSON> '<PERSON>'", "Show arrow buttons on the last in-chat message to generate alternative AI responses. Both PC and mobile": "Afficher des boutons fléchés sur le dernier message de discussion pour générer des réponses alternatives de l'IA. Sur PC et mobile", "Swipes": "Balayages", "Allow using swiping gestures on the last in-chat message to trigger swipe generation. Mobile only, no effect on PC": "Autoriser l'utilisation de gestes de balayage sur le dernier message de discussion pour déclencher la génération de messages alternatifs. Mobile uniquement, aucun effet sur PC", "Gestures": "<PERSON><PERSON><PERSON>", "Auto-load Last Chat": "Chargement automatique de la dernière discussion", "Auto-scroll Chat": "Défilement automatique de la discussion", "Save edits to messages without confirmation as you type": "Enregistrer les modifications apportées aux messages sans confirmation pendant la saisie", "Auto-save Message Edits": "Sauvegarde automatique des modifications de message", "Confirm message deletion": "Confirmer la suppression du message", "Auto-fix Markdown": "Correction automatique du Markdown", "Disallow embedded media from other domains in chat messages": "Interdire les médias intégrés provenant d'autres domaines dans les messages de discussion.", "Forbid External Media": "Interdire les médias externes", "Allow {{char}}: in bot messages": "Autoriser {{char}} : dans les messages du bot", "Allow {{user}}: in bot messages": "Autoriser {{user}} : dans les messages du bot", "Skip encoding  and  characters in message text, allowing a subset of HTML markup as well as Markdown": "Ignorer l'encodage et les caractères < et > dans le texte du message, permettant un sous-ensemble du balisage HTML ainsi que Markdown", "Show tags in responses": "Afficher les tags dans les réponses", "Allow AI messages in groups to contain lines spoken by other group members": "Autoriser les messages de l'IA dans les groupes à contenir des lignes prononcées par d'autres membres du groupe", "Relax message trim in Groups": "Re<PERSON>er la taille des messages dans les groupes", "Log prompts to console": "Journaliser les prompts dans la console", "Requests logprobs from the API for the Token Probabilities feature": "Demande des logprobs de l'API pour la fonctionnalité des probabilités des tokens", "Request token probabilities": "Demande des probabilités de tokens", "Automatically reject and re-generate AI message based on configurable criteria": "Rejeter automatiquement et régénérer un message AI en fonction de critères configurables", "Auto-swipe": "Balayage automatique", "Enable the auto-swipe function. Settings in this section only have an effect when auto-swipe is enabled": "Activer la fonction de balayage automatique. Les paramètres de cette section n'ont d'effet que lorsque le balayage automatique est activé", "Minimum generated message length": "Longueur minimale du message généré", "If the generated message is shorter than these many characters, trigger an auto-swipe": "Si le message généré est plus court que cela, déclenchez un balayage automatique", "Blacklisted words": "Mots en liste noire", "words you dont want generated separated by comma ','": "mots que vous ne voulez pas générer séparés par des virgules ','", "Blacklisted word count to swipe": "Nombre de mots en liste noire pour balayer", "Minimum number of blacklisted words detected to trigger an auto-swipe": "Nombre minimum de mots en liste noire détectés pour déclencher un balayage automatique", "AutoComplete Settings": "Paramètres de saisie semi-automatique", "Automatically hide details": "Masquer automatiquement les détails", "Determines how entries are found for autocomplete.": "Détermine comment les entrées sont trouvées pour la saisie semi-automatique.", "Autocomplete Matching": "Correspondant à", "Starts with": "Commence avec", "Includes": "Comprend", "Fuzzy": "<PERSON><PERSON>", "Sets the style of the autocomplete.": "Définit le style de la saisie semi-automatique.", "Autocomplete Style": "Style", "Follow Theme": "<PERSON><PERSON><PERSON> le thème", "Dark": "Sombre", "Sets the font size of the autocomplete.": "Définit la taille de la police de la saisie semi-automatique.", "Sets the width of the autocomplete.": "Définit la largeur de la saisie semi-automatique.", "Autocomplete Width": "<PERSON><PERSON>", "chat input box": "zone de saisie du chat", "entire chat width": "toute la largeur du chat", "full window width": "pleine largeur de fenêtre", "STscript Settings": "Paramètres STscript", "Sets default flags for the STscript parser.": "Définit les indicateurs par défaut pour l'analyseur STscript.", "Parser Flags": "Indicateurs de l'analyseur", "Switch to stricter escaping, allowing all delimiting characters to be escaped with a backslash, and backslashes to be escaped as well.": "Passez à un échappement plus strict, permettant à tous les caractères de délimitation d'être échappés avec une barre oblique inverse, ainsi qu'aux barres obliques inverses.", "STRICT_ESCAPING": "STRICT_ESCAPING", "REPLACE_GETVAR": "REPLACE_GETVAR", "Change Background Image": "Changer l'arrière-plans", "Filter": "Filtre", "Automatically select a background based on the chat context": "Sélectionner automatiquement un arrière-plan en fonction du contexte de la discussion", "Auto-select": "Sélection automatique", "System Backgrounds": "Arrière-plans du système", "Chat Backgrounds": "Arrière-plans de la discussion", "bg_chat_hint_1": "Les arrière-plans générés avec l'extension ", "bg_chat_hint_2": " apparaîtrons ici.", "Extensions": "Extensions", "Notify on extension updates": "Notifier les mises à jour des extensions", "Manage extensions": "<PERSON><PERSON><PERSON> les extensions", "Import Extension From Git Repo": "Importer une extension depuis un dépôt Git", "Install extension": "Installer une extension", "Extras API:": "API supplémentaires :", "Auto-connect": "Connexion automatique", "Extras API URL": "URL de l'API des extras", "Extras API key (optional)": "Clé API supplémentaire (facultatif)", "Persona Management": "Gestion des personas", "Click for stats!": "Cliquez pour les statistiques!", "Usage Stats": "Statistiques d'utilisation", "Backup your personas to a file": "Sauvegardez vos personas dans un fichier", "Backup": "<PERSON><PERSON><PERSON><PERSON>", "Restore your personas from a file": "<PERSON><PERSON><PERSON> vos personas à partir d'un fichier", "Restore": "<PERSON><PERSON><PERSON>", "Create a dummy persona": "<PERSON><PERSON><PERSON> une persona factice", "Create": "<PERSON><PERSON><PERSON>", "Toggle grid view": "Basculer en mode grille", "No persona description": "[Pas de description]", "Name": "Nom", "Enter your name": "Entrez votre nom", "Click to set a new User Name": "Cliquez pour définir un nouveau nom d'utilisateur", "Click to lock your selected persona to the current chat. Click again to remove the lock.": "Cliquez pour verrouiller votre persona sélectionnée sur la discussion actuelle. Cliquez à nouveau pour supprimer le verrou.", "Click to set user name for all messages": "Cliquez pour définir le nom d'utilisateur pour tous les messages", "Persona Description": "Description de la persona", "Example: [{{user}} is a 28-year-old Romanian cat girl.]": "Exemple : [{{user}} est une fille chat roumaine de 28 ans.]", "Tokens persona description": "Description des tokens", "Position:": "Position :", "In Story String / Prompt Manager": "Dans la chaîne d'histoire / Gestionnaire de prompt", "Top of Author's Note": "En haut de la note d'auteur", "Bottom of Author's Note": "En bas de la note d'auteur", "In-chat @ Depth": "Dans le chat @ Profondeur", "Depth:": "Profondeur :", "Role:": "Rôle:", "System": "Système", "User": "Utilisa<PERSON>ur", "Assistant": "Assistant", "Show notifications on switching personas": "Afficher les notifications lors du changement de persona", "Character Management": "Gestion des personnages", "Locked = Character Management panel will stay open": "Verrouillé = le panneau de gestion des personnages restera ouvert", "Select/Create Characters": "Sélectionner/Créer des personnages", "Favorite characters to add them to HotSwaps": "Mettre les personnages en favoris pour les ajouter aux HotSwaps", "Token counts may be inaccurate and provided just for reference.": "Le comptage des tokens peut être inexacts et est fournis uniquement à titre de référence.", "Total tokens": "Total des tokens", "Calculating...": "Calcul en cours...", "Tokens": "Tokens", "Permanent tokens": "Tokens permanents", "Permanent": "Permanent", "About Token 'Limits'": "À propos des « limites » des tokens", "Toggle character info panel": "Basculer le panneau d'informations sur le personnage", "Name this character": "Nommer ce personnage", "extension_token_counter": "Tokens :", "Click to select a new avatar for this character": "Cliquez pour sélectionner un nouvel avatar pour ce personnage", "Add to Favorites": "Ajouter aux favoris", "Advanced Definition": "Définition avancée", "Chat Lore": "<PERSON><PERSON> du chat", "Export and Download": "Exporter et Télécharger", "Duplicate Character": "<PERSON>p<PERSON><PERSON> le personnage", "Create Character": "<PERSON><PERSON><PERSON> un personnage", "Delete Character": "Supprimer le personnage", "More...": "Plus...", "Link to World Info": "Lien vers le World Info", "Import Card Lore": "Importer le lore de la carte", "Scenario Override": "Remplacement du scénario", "Convert to Persona": "Convertir en Persona", "Rename": "<PERSON>mmer", "Link to Source": "Lien vers la source", "Replace / Update": "Remplacer/Mettre à jour", "Import Tags": "Importer les tags", "Search / Create Tags": "<PERSON><PERSON><PERSON> / <PERSON><PERSON><PERSON>", "View all tags": "Voir tout les tags", "Creator's Notes": "Notes du créateur", "Show / Hide Description and First Message": "Afficher / Masquer la description et le premier message", "Character Description": "Description du personnage", "Click to allow/forbid the use of external media for this character.": "Cliquez pour autoriser/interdire l’utilisation de médias externes pour ce personnage.", "Ext. Media": "Médias Ext.", "Describe your character's physical and mental traits here.": "D<PERSON><PERSON><PERSON>z les traits physiques et mentaux de votre personnage ici.", "First message": "Premier message", "Click to set additional greeting messages": "Cliquez pour définir des messages de salutation supplémentaires", "Alt. Greetings": "Salutations Alt.", "This will be the first message from the character that starts every chat.": "Ce sera le premier message du personnage qui démarre chaque discussion.", "Group Controls": "Contrôles de groupe", "Chat Name (Optional)": "Nom de la discussion (Facultatif)", "Click to select a new avatar for this group": "Cliquez pour sélectionner un nouvel avatar pour ce groupe", "Group reply strategy": "Stratégie de réponse de groupe", "Natural order": "Ordre naturel", "List order": "Ordre de la liste", "Group generation handling mode": "Mode de gestion de la génération de groupe", "Swap character cards": "Échanger les cartes de personnages", "Join character cards (exclude muted)": "Joignez les cartes de personnage (excluez les cartes en sourdine)", "Join character cards (include muted)": "Joignez les cartes de personnage (y compris en sourdine)", "Inserted before each part of the joined fields.": "Inséré avant chaque partie des champs joints.", "Join Prefix": "Jo<PERSON><PERSON> les préfixes", "When 'Join character cards' is selected, all respective fields of the characters are being joined together.This means that in the story string for example all character descriptions will be joined to one big text.If you want those fields to be separated, you can define a prefix or suffix here.This value supports normal macros and will also replace {{char}} with the relevant char's name and <FIELDNAME> with the name of the part (e.g.: description, personality, scenario, etc.)": "Lorsque « <PERSON><PERSON>re les cartes de personnage » est sélectionné, tous les champs respectifs des personnages sont réunis.\rCela signifie que dans la chaîne d'histoire, par exemple, toutes les descriptions des personnages seront réunies en un seul grand texte.\rSi vous souhaitez que ces champs soient séparés, vous pouvez définir ici un préfixe ou un suffixe.\r\rCette valeur prend en charge les macros normales et remplacera également {{char}} par le nom du caractère concerné et <FIELDNAME> par le nom de la pièce (par exemple : description, personnalité, scénario, etc.)", "Inserted after each part of the joined fields.": "Inséré après chaque partie des champs joints.", "Join Suffix": "Suffixe de jointure", "Set a group chat scenario": "Définir un scénario de discussion de groupe", "Click to allow/forbid the use of external media for this group.": "Cliquez pour autoriser/interdire l’utilisation de médias externes pour ce groupe.", "Restore collage avatar": "Restaurer l'avatar du collage", "Allow self responses": "Autoriser les réponses à soi-même", "Auto Mode": "Mode automatique", "Auto Mode delay": "Délai du mode automatique", "Hide Muted Member Sprites": "Masquer les sprites des membres muets", "Current Members": "Membres actuels", "Add Members": "Ajouter des membres", "Create New Character": "<PERSON><PERSON>er un nouveau personnage", "Import Character from File": "Importer un personnage à partir d'un fichier", "Import content from external URL": "Importer du contenu depuis une URL externe", "Create New Chat Group": "Créer une nouvelle discussion de groupe", "Characters sorting order": "Ordre des personnages", "A-Z": "A-Z", "Z-A": "Z-A", "Newest": "Plus récent", "Oldest": "Plus ancien", "Favorites": "<PERSON><PERSON><PERSON>", "Recent": "<PERSON><PERSON><PERSON>", "Most chats": "Plus de discussions", "Least chats": "Moins de <PERSON>", "Most tokens": "Tokens maximum", "Least tokens": "Tokens minimum", "Random": "Aléatoire", "Toggle character grid view": "Basculer vers la vue en grille des personnages", "Bulk_edit_characters": "Édition en masse des personnages", "Bulk select all characters": "Sélection groupée de tous les caractères", "Bulk delete characters": "Suppression en masse des personnages", "popup-button-save": "<PERSON><PERSON><PERSON><PERSON>", "popup-button-yes": "O<PERSON>", "popup-button-no": "Non", "popup-button-cancel": "Annuler", "popup-button-import": "Importer", "Advanced Definitions": "Définitions avancées", "Prompt Overrides": "Remplacements d'invite", "(For Chat Completion and Instruct Mode)": "(Pour l'achèvement du chat et le mode instruction)", "Insert {{original}} into either box to include the respective default prompt from system settings.": "Ins<PERSON>rez {{original}} dans l'un ou l'autre des champs pour inclure l'instruction par défaut respective des paramètres système.", "Main Prompt": "Instruction principale", "Any contents here will replace the default Main Prompt used for this character. (v2 spec: system_prompt)": "Tout contenu ici remplacera l'instruction principale par défaut utilisée pour ce personnage. (spécification v2 : système_prompt)", "Any contents here will replace the default Jailbreak Prompt used for this character. (v2 spec: post_history_instructions)": "Tout contenu ici remplacera l'instruction de jailbreak par défaut utilisée pour ce personnage. (spécification v2 : post_history_instructions)", "Creator's Metadata (Not sent with the AI prompt)": "Métadonnées du créateur (Non envoyées avec l'instruction de l'IA)", "Creator's Metadata": "Métadonnées du créateur", "(Not sent with the AI Prompt)": "(Non envoyé avec le prompt IA)", "Everything here is optional": "Tout ce qui se trouve ici est facultatif", "(Botmaker's name / Contact Info)": "(Nom du créateur du bot / Informations de contact)", "(If you want to track character versions)": "(Si vous voulez suivre les versions du personnage)", "(Describe the bot, give use tips, or list the chat models it has been tested on. This will be displayed in the character list.)": "(<PERSON><PERSON><PERSON><PERSON><PERSON> le bot, donnez des conseils d'utilisation ou répertoriez les modèles de discussion sur lesquels il a été testé. <PERSON><PERSON> s'affichera dans la liste des personnages.)", "Tags to Embed": "Tags à intégrer", "(Write a comma-separated list of tags)": "(Éc<PERSON>z une liste de tags séparés par des virgules)", "Personality summary": "Résumé de la personnalité", "(A brief description of the personality)": "(Une brève description de la personnalité)", "Scenario": "<PERSON><PERSON><PERSON>", "(Circumstances and context of the interaction)": "(Circonstances et contexte de l'interaction)", "Character's Note": "Note du personnage", "(Text to be inserted in-chat @ designated depth and role)": "(Texte à insérer dans le chat @ profondeur et rôle désignés)", "@ Depth": "@ Profondeur", "Role": "R<PERSON><PERSON>", "Talkativeness": "Bavardage", "How often the character speaks in group chats!": "À quelle fréquence le personnage parle dans les discussions de groupe !", "How often the character speaks in": "À quelle fréquence le personnage parle", "group chats!": "les discussions de groupe !", "Shy": "Timide", "Normal": "Normal", "Chatty": "<PERSON><PERSON>", "Examples of dialogue": "Exemples de dialogue", "Important to set the character's writing style.": "Important de définir le style d'écriture du personnage.", "(Examples of chat dialog. Begin each example with START on a new line.)": "(Exemples de dialogue. Commencez chaque exemple par START sur une nouvelle ligne.)", "Save": "Enregistrer", "Chat History": "Historique de la conversation", "Import Chat": "Importer une discussion", "Copy to system backgrounds": "<PERSON><PERSON>r vers les arrière-plans du système", "Rename background": "Renommer l'arrière-plan", "Lock": "Verrouiller", "Unlock": "Déverrouiller", "Delete background": "Supprimer l'arrière-plan", "Chat Scenario Override": "Remplacement du scénario de discussion", "Remove": "<PERSON><PERSON><PERSON><PERSON>", "Type here...": "Tapez ici...", "Chat Lorebook": "Lorebook pour", "Chat Lorebook for": "Chat Lorebook pour", "chat_world_template_txt": "Un World Info sélectionné sera lié à ce chat. Lors de la génération d'une réponse IA,\n                    il sera combiné avec les entrées des livres de connaissances globaux et de personnages.", "Select a World Info file for": "Sélectionnez un fichier Word Info pour", "Primary Lorebook": "Lorebook principal", "A selected World Info will be bound to this character as its own Lorebook.": "Un World Info sélectionnée sera liée à ce personnage comme son propre lorebook.", "When generating an AI reply, it will be combined with the entries from a global World Info selector.": "Lors de la génération d'une réponse de l'IA, elle sera combinée avec les entrées d'un sélecteur de World Info global.", "Exporting a character would also export the selected Lorebook file embedded in the JSON data.": "L'exportation d'un personnage exporterait également le fichier de lorebook sélectionné incorporé dans les données JSON.", "Additional Lorebooks": "Lorebooks supplémentaires", "Associate one or more auxillary Lorebooks with this character.": "Associer un ou plusieurs lorebooks auxiliaires à ce personnage.", "NOTE: These choices are optional and won't be preserved on character export!": "REMARQUE : Ces choix sont facultatifs et ne seront pas conservés lors de l'exportation du personnage !", "Rename chat file": "Renommer le fichier de discussion", "Export JSONL chat file": "Exporter le fichier de discussion au format JSONL", "Download chat as plain text document": "Télécharger la discussion sous forme de document texte brut", "Delete chat file": "Supp<PERSON><PERSON> le fichier de discussion", "Use tag as folder": "Utiliser les tags comme dossier", "Hide on character card": "Masquer sur la fiche du personnage", "Delete tag": "Supprimer le tag'", "Entry Title/Memo": "Titre de l'entrée/Mémo", "WI_Entry_Status_Constant": "<PERSON><PERSON><PERSON>", "WI_Entry_Status_Normal": "Normale", "WI_Entry_Status_Vectorized": "Vectorisé", "T_Position": "↑Personnage : avant les définitions de personnage\n↓Personnage : après les définitions de personnage\n↑AN : avant les notes d'auteur\n↓AN : après les notes d'auteur\n@D : à la profondeur", "Before Char Defs": "Avant les définitions de personnage", "After Char Defs": "Après les définitions de personnage", "Before EM": "↑EM", "After EM": "↓EM", "Before AN": "Avant NA", "After AN": "Après NA", "at Depth System": "@D ⚙️", "at Depth User": "@D 👤", "at Depth AI": "@D 🤖", "Depth": "<PERSON><PERSON><PERSON>", "Order:": "Ordre :", "Order": "Ordre :", "Trigger %:": "% Déclenchement :", "Probability": "Probabilité", "Duplicate world info entry": "Dupliquer l'entrée du World Info", "Delete world info entry": "Supprimer l'entrée du World Info", "Comma separated (required)": "Séparé par des virgules (requis)", "Primary Keywords": "Mots-clés princi<PERSON>ux", "Keywords or Regexes": "Mots-clés ou expressions régulières", "Comma separated list": "Liste séparée par des virgules", "Switch to plaintext mode": "Passer en mode texte brut", "Logic": "Logique", "AND ANY": "ET TOUT", "AND ALL": "ET TOUS", "NOT ALL": "PAS TOUS", "NOT ANY": "PAS DE TOUS", "(ignored if empty)": "(ignoré si vide)", "Optional Filter": "Filtre optionnel", "Keywords or Regexes (ignored if empty)": "Mots-clés ou expressions régulières (ignorés s'ils sont vides)", "Comma separated list (ignored if empty)": "Liste séparée par des virgules (ignorée si vide)", "Use global setting": "Utiliser le paramètre global", "Case-Sensitive": "Sensible à la casse", "Yes": "O<PERSON>", "No": "Non", "Can be used to automatically activate Quick Replies": "Peut être utilisé pour activer automatiquement les Quick Replies", "Automation ID": "ID d'automatisation", "( None )": "( Aucun )", "Content": "Contenu", "Prevent further recursion (this entry will not activate others)": "Empêcher toute récursion ultérieure (cette entrée n’en activera pas d’autres)", "What this keyword should mean to the AI, sent verbatim": "Ce que ce mot-clé devrait signifier pour l'IA, envoyé textuellement", "-- Characters not found --": "-- Personnages non trouvés --", "Inclusion Group": "Groupe d'inclusion", "Inclusion Groups ensure only one entry from a group is activated at a time, if multiple are triggered.Documentation: World Info - Inclusion Group": "Les groupes d'inclusion garantissent qu'une seule entrée d'un groupe est activée à la fois, si plusieurs sont déclenchées.\rPrend en charge plusieurs groupes séparés par des virgules.\r\rDocumentation : World Info - Groupe Inclusion", "Prioritize this entry: When checked, this entry is prioritized out of all selections.If multiple are prioritized, the one with the highest 'Order' is chosen.": "Prioriser cette entrée : Lorsque cette case est cochée, cette entrée est prioritaire parmi toutes les sélections.\rSi plusieurs sont prioritaires, celui avec l'ordre le plus élevé est choisi.", "Only one entry with the same label will be activated": "Seule une entrée avec la même étiquette sera activée", "A relative likelihood of entry activation within the group": "Une probabilité relative d’activation d’entrée au sein du groupe", "Group Weight": "Poids du groupe", "Selective": "Sélectif", "Use Probability": "Utiliser la probabilité", "Add Memo": "Ajouter un mémo", "Text or token ids": "Texte ou [token ids]", "close": "fermer", "prompt_manager_edit": "Modifier", "prompt_manager_name": "Nom", "A name for this prompt.": "Un nom pour ce prompt.", "To whom this message will be attributed.": "À qui ce message sera attribué.", "AI Assistant": "Assistant IA", "prompt_manager_position": "Position", "prompt_manager_relative": "Relatif", "prompt_manager_depth": "<PERSON><PERSON><PERSON>", "0 = after the last message, 1 = before the last message, etc.": "0 = apr<PERSON> le dernier message, 1 = avant le dernier message, etc.", "Prompt": "Prompt", "The prompt to be sent.": "Le prompt à envoyer.", "This prompt cannot be overridden by character cards, even if overrides are preferred.": "Ce prompt ne peut pas être remplacé par les cartes de personnage, même si les remplacements sont préférés.", "prompt_manager_forbid_overrides": "Interdire les remplacements", "reset": "réinitialiser", "save": "sauve<PERSON><PERSON>", "This message is invisible for the AI": "Ce message est invisible pour l'IA", "Message Actions": "Actions des messages", "Translate message": "Traduire le message", "Generate Image": "<PERSON><PERSON><PERSON>rer une image", "Narrate": "<PERSON><PERSON>", "Exclude message from prompts": "Exclure le message des prompts", "Include message in prompts": "Inclure le message dans les prompts", "Embed file or image": "Intégrer un fichier ou une image", "Create checkpoint": "Créer un point de contrôle", "Create Branch": "C<PERSON>er une branche", "Copy": "<PERSON><PERSON><PERSON>", "Edit": "Modifier", "Confirm": "Confirmer", "Copy this message": "Copier ce message", "Delete this message": "Supprimer ce message", "Move message up": "<PERSON><PERSON><PERSON><PERSON> le message vers le haut", "Move message down": "<PERSON><PERSON><PERSON><PERSON> le message vers le bas", "Enlarge": "<PERSON><PERSON><PERSON><PERSON>", "Welcome to SillyTavern!": "Bienvenue sur SillyTavern !", "welcome_message_part_1": "Lisez la", "welcome_message_part_2": "Documentation officielle", "welcome_message_part_3": null, "welcome_message_part_4": "Tapez", "welcome_message_part_5": "dans le chat pour les commandes et les macros.", "welcome_message_part_6": "<PERSON><PERSON><PERSON><PERSON> le", "Discord server": "<PERSON><PERSON><PERSON>", "welcome_message_part_7": "pour les infos et annonces.", "SillyTavern is aimed at advanced users.": "<PERSON><PERSON>T<PERSON>n s'adresse aux utilisateurs avancés.", "Looking for AI characters?": "Vous recherchez des personnages ?", "onboarding_import": "Importer", "from supported sources or view": "à partir de sources prises en charge ou afficher des", "Sample characters": "Exemples de personnages", "Your Persona": "Votre persona", "Before you get started, you must select a persona name.": "Avant de commencer, vous devez sélectionner un nom pour votre persona.", "welcome_message_part_8": "<PERSON>ci peut être modifié à tout moment via l'icône", "welcome_message_part_9": ".", "Persona Name:": "Nom de la persona :", "Temporarily disable automatic replies from this character": "Désactiver temporairement les réponses automatiques de ce personnage", "Enable automatic replies from this character": "Activer les réponses automatiques de ce personnage", "Trigger a message from this character": "Déclencher un message de ce personnage", "Move up": "<PERSON><PERSON>", "Move down": "Descendre", "View character card": "Voir la carte du personnage", "Remove from group": "Retirer du groupe", "Add to group": "Ajouter au groupe", "Alternate Greetings": "Salutations alternatives", "Alternate_Greetings_desc": "Ceux-ci seront affichés sous forme de balayages sur le premier message lors du démarrage d’une nouvelle discussion.\n                Les membres du groupe peuvent en sélectionner un pour lancer la conversation.", "(This will be the first message from the character that starts every chat)": "(Ce sera le premier message du personnage qui démarre chaque discussion)", "Forbid Media Override explanation": "Capacité du personnage/groupe actuel à utiliser des médias externes dans les discussions.", "Forbid Media Override subtitle": "Médias : images, vidéos, audio. Externe : non hébergé sur le serveur local.", "Always forbidden": "Toujours interdit", "Always allowed": "Toujours permis", "View contents": "Voir le contenu", "Remove the file": "<PERSON><PERSON><PERSON><PERSON> le fichier", "Unique to this chat": "Unique à ce chat", "Checkpoints inherit the Note from their parent, and can be changed individually after that.": "Les points de contrôle héritent de la note de leur parent et peuvent ensuite être modifiés individuellement.", "Include in World Info Scanning": "Inclure dans l'analyse des Wold Info", "Before Main Prompt / Story String": "Avant le prompt principal/la chaîne d'histoire", "After Main Prompt / Story String": "Après le prompt principal/la chaîne d'histoire", "as": "comme", "Insertion Frequency": "Fréquence d'insertion", "(0 = Disable, 1 = Always)": "(0 = <PERSON><PERSON><PERSON><PERSON>, 1 = <PERSON>u<PERSON>rs)", "User inputs until next insertion:": "Saisies de l'utilisateur jusqu'à la prochaine insertion :", "Character Author's Note (Private)": "Note d'auteur du personnage (privé)", "Won't be shared with the character card on export.": "Ne sera pas partagé avec la carte de personnage lors de l'exportation.", "Will be automatically added as the author's note for this character. Will be used in groups, but can't be modified when a group chat is open.": "Sera automatiquement ajouté comme note d'auteur pour ce personnage. Sera utilisé en groupe, mais\n                            ne peut pas être modifié lorsqu'une discussion de groupe est ouverte.", "Use character author's note": "Utiliser la note d'auteur du personnage", "Replace Author's Note": "Remplacer la note d'auteur", "Default Author's Note": "Note d'auteur par défaut", "Will be automatically added as the Author's Note for all new chats.": "Sera automatiquement ajouté en tant que note d'auteur pour toutes les nouvelles discussions.", "Chat CFG": "Chat CFG", "1 = disabled": "1 = d<PERSON><PERSON><PERSON><PERSON>", "write short replies, write replies using past tense": "rédiger des réponses courtes, rédiger des réponses en utilisant le passé", "Positive Prompt": "Prompt positif", "Use character CFG scales": "Utiliser les échelles CFG du personnage", "Character CFG": "CFG du personnage", "Will be automatically added as the CFG for this character.": "Sera automatiquement ajouté en tant que CFG pour ce personnage.", "Global CFG": "CFG global", "Will be used as the default CFG options for every chat unless overridden.": "Sera utilisé comme options CFG par défaut pour chaque discussion, sauf remplacement.", "CFG Prompt Cascading": "Prompt CFG en cascade", "Combine positive/negative prompts from other boxes.": "Combinez les prompts positifs/négatifs d’autres cases.", "For example, ticking the chat, global, and character boxes combine all negative prompts into a comma-separated string.": "Par exemple, en cochant les cases Chat, Global et Personnage, vous combinez tout les prompt négatifs dans une chaîne séparée par des virgules.", "Always Include": "Toujours inclure", "Chat Negatives": "Points négatifs du chat", "Character Negatives": "Points négatifs du personnage", "Global Negatives": "Points négatifs globaux", "Custom Separator:": "Séparateur personnalisé :", "Insertion Depth:": "Profondeur d'insertion :", "Token Probabilities": "Probabilités des tokens", "Select a token to see alternatives considered by the AI.": "Sélectionnez un token pour voir les alternatives envisagées par l'IA.", "Not connected to API!": "Non connecté à l'API !", "Type a message, or /? for help": "Tapez un message, ou /? pour l'aide", "Continue script execution": "Continuer l'exécution du script", "Pause script execution": "Suspendre l'exécution du script", "Abort script execution": "Abandonner l'exécution du script", "Abort request": "Annuler la requête", "Continue the last message": "<PERSON><PERSON><PERSON> le dernier message", "Send a message": "Envoyer un message", "Close chat": "<PERSON><PERSON><PERSON> la <PERSON>", "Toggle Panels": "Basculer les panneaux", "Back to parent chat": "Retour à la conversation parente", "Save checkpoint": "Enregistrer le point de contrôle", "Convert to group": "Convertir en groupe", "Start new chat": "<PERSON><PERSON><PERSON><PERSON> une nouvelle conversation", "Manage chat files": "<PERSON><PERSON><PERSON> les fichiers de discussion", "Delete messages": "Supprimer les messages", "Regenerate": "<PERSON><PERSON><PERSON><PERSON>", "Ask AI to write your message for you": "<PERSON><PERSON><PERSON> à l'IA d'écrire votre message pour vous", "Impersonate": "Usurper l'identité", "Continue": "<PERSON><PERSON><PERSON>", "Bind user name to that avatar": "Lier le nom d'utilisateur à cette persona", "Change persona image": "Changer l'image de la persona", "Select this as default persona for the new chats.": "Sélectionner ceci comme persona par défaut pour les nouvelles discussions.", "Delete persona": "Supprimer la persona", "These characters are the winners of character design contests and have outstandable quality.": "Ces personnages sont les gagnants de concours de conception de personnages et ont une qualité exceptionnelle.", "Contest Winners": "Gagnants du concours", "These characters are the finalists of character design contests and have remarkable quality.": "Ces personnages sont les finalistes des concours de conception de personnages et ont une qualité remarquable.", "Featured Characters": "Personnages en vedette", "Attach a File": "<PERSON><PERSON><PERSON> un fichier", "Open Data Bank": "Ouvrir la banque de données", "Enter a URL or the ID of a Fandom wiki page to scrape:": "Entrez une URL ou l'ID d'une page wiki Fandom à  :", "Examples:": "Exemples:", "Example:": "Exemple:", "Single file": "Un seul fichier", "All articles will be concatenated into a single file.": "Tous les articles seront concaténés dans un seul fichier.", "File per article": "Fichier par article", "Each article will be saved as a separate file.": "Non recommandé. Chaque article sera enregistré dans un fichier distinct.", "Data Bank": "Banque de données", "These files will be available for extensions that support attachments (e.g. Vector Storage).": "Ces fichiers seront disponibles pour les extensions prenant en charge les pièces jointes (par exemple Vector Storage).", "Supported file types: Plain Text, PDF, Markdown, HTML, EPUB.": "Types de fichiers pris en charge : texte brut, PDF, Markdown, HTML, EPUB.", "Drag and drop files here to upload.": "Faites glisser et déposez les fichiers ici pour les télécharger.", "Date (Newest First)": "Date (le plus récent en premier)", "Date (Oldest First)": "Date (le plus ancien en premier)", "Name (A-Z)": "Nom (A-Z)", "Name (Z-A)": "Nom (Z-A)", "Size (Smallest First)": "<PERSON><PERSON> (la plus petite en premier)", "Size (Largest First)": "<PERSON><PERSON> (la plus grande en premier)", "Bulk Edit": "Modification groupée", "Select All": "<PERSON><PERSON>", "Select None": "<PERSON>e rien sélectionner", "Global Attachments": "Pièces jointes globales", "These files are available for all characters in all chats.": "Ces fichiers sont disponibles pour tous les personnages de toutes les discussions.", "Character Attachments": "Pièces jointes aux personnages", "Saved locally. Not exported.": "Enregistré localement. Non exporté.", "Chat Attachments": "Pièces jointes au chat", "Enter a base URL of the MediaWiki to scrape.": "Entrez une URL de base du MediaWiki à récupérer.", "Don't include the page name!": "N'incluez pas le nom de la page !", "Enter web URLs to scrape (one per line):": "Entrez les URL Web à récupérer (une par ligne) :", "Enter a video URL to download its transcript.": "Saisissez l'URL de la vidéo pour télécharger sa transcription.", "ext_sum_with": "Résumez avec :", "ext_sum_main_api": "API principale", "ext_sum_current_summary": "Résumé actuel :", "ext_sum_restore_previous": "R<PERSON><PERSON><PERSON><PERSON>r le précédente", "ext_sum_memory_placeholder": "Le résumé sera généré ici...", "ext_sum_force_text": "Résumez maintenant", "Disable automatic summary updates. While paused, the summary remains as-is. You can still force an update by pressing the Summarize now button (which is only available with the Main API).": "Désactivez les mises à jour automatiques du résumé. Pendant la pause, le résumé reste tel quel. Vous pouvez toujours forcer une mise à jour en appuyant sur le bouton Résumer maintenant (qui n'est disponible qu'avec l'API principale).", "ext_sum_pause": "Pause", "Omit World Info and Author's Note from text to be summarized. Only has an effect when using the Main API. The Extras API always omits WI/AN.": "Omettre World Info et la note d'auteur du texte à résumer. N'a d'effet que lors de l'utilisation de l'API principale. L'API Extras omet toujours WI/AN.", "ext_sum_no_wi_an": "Pas de WI/NA", "ext_sum_settings_tip": "Modifier le prompt de résumé, la position d'insertion, etc.", "ext_sum_settings": "Paramètres du résumé", "ext_sum_prompt_builder": "Générateur de prompt", "ext_sum_prompt_builder_1_desc": "L'extension créera son propre prompt en utilisant des messages qui n'ont pas encore été résumés. Bloque le chat jusqu'à ce que le résumé soit généré.", "ext_sum_prompt_builder_1": "<PERSON><PERSON><PERSON>, b<PERSON><PERSON>nt", "ext_sum_prompt_builder_2_desc": "L'extension créera son propre prompt en utilisant des messages qui n'ont pas encore été résumés. Ne bloque pas le chat pendant la génération du résumé. Tous les backends ne prennent pas en charge ce mode.", "ext_sum_prompt_builder_2": "<PERSON><PERSON><PERSON>, non bloquant", "ext_sum_prompt_builder_3_desc": "L'extension utilisera le générateur de prompt principal standard et y ajoutera la demande de résumé comme dernier message système.", "ext_sum_prompt_builder_3": "Classique, bloquant", "Summary Prompt": "Prompt de résumé", "ext_sum_restore_default_prompt_tip": "Restaurer le prompt par défaut", "ext_sum_prompt_placeholder": "Ce prompt sera envoyée à l'IA pour demander la génération du résumé. {{words}} sera résolu par le paramètre \"Nombre de mots\".", "ext_sum_target_length_1": "Longueur cible du résumé", "ext_sum_target_length_2": null, "ext_sum_target_length_3": "mots)", "ext_sum_api_response_length_1": "Longueur de réponse de l'API", "ext_sum_api_response_length_2": null, "ext_sum_api_response_length_3": "tokens)", "ext_sum_0_default": "0 = par défaut", "ext_sum_raw_max_msg": "[Brut] Nombre maximum de messages par requête", "ext_sum_0_unlimited": "0 = illimité", "Update frequency": "<PERSON><PERSON><PERSON> de mise à jour", "ext_sum_update_every_messages_1": "Mettre à jour tous les", "ext_sum_update_every_messages_2": "messages", "ext_sum_0_disable": "0 = d<PERSON><PERSON>r", "ext_sum_auto_adjust_desc": "Essayez d'ajuster automatiquement l'intervalle en fonction des mesures du chat.", "ext_sum_update_every_words_1": "Mettre à jour tous les", "ext_sum_update_every_words_2": "mots", "ext_sum_both_sliders": "Si les deux curseurs sont différents de <PERSON>, ils déclencheront tous deux des mises à jour récapitulatives à leurs intervalles respectifs.", "ext_sum_injection_template": "<PERSON><PERSON><PERSON><PERSON> d'<PERSON>", "ext_sum_memory_template_placeholder": "{{summary}} sera résolu selon le contenu du résumé actuel.", "ext_sum_injection_position": "Position d'injection", "How many messages before the current end of the chat.": "Combien de messages avant la fin actuelle du chat.", "ext_regex_title": "Expression régulière", "ext_regex_new_global_script": "+ Mondial", "ext_regex_new_scoped_script": "+ Portée", "ext_regex_import_script": "Importer", "ext_regex_global_scripts": "Scripts globaux", "ext_regex_global_scripts_desc": "Disponible pour tous les personnages. Enregistré dans les paramètres locaux.", "ext_regex_scoped_scripts": "<PERSON><PERSON>ts étendus", "ext_regex_scoped_scripts_desc": "Uniquement disponible pour ce personnage. Enregistré dans les données de la carte.", "Regex Editor": "Éditeur d'expressions régulières", "Test Mode": "Mode de test", "ext_regex_desc": "Regex est un outil pour rechercher/remplacer des chaînes à l'aide d'expressions régulières. Si vous souhaitez en savoir plus, cliquez sur ? à côté du titre.", "Input": "<PERSON><PERSON>", "ext_regex_test_input_placeholder": "Écrivez ici...", "Output": "Sortir", "ext_regex_output_placeholder": "Vide", "Script Name": "Nom du script", "Find Regex": "Trouver une expression régulière", "Replace With": "Remplacer par", "ext_regex_replace_string_placeholder": "Utilisez {{match}} pour inclure le texte correspondant de Find Regex ou $1, $2, etc. pour les groupes de capture.", "Trim Out": "Couper", "ext_regex_trim_placeholder": "Supprime globalement toutes les pièces indésirables d’une correspondance d’expression régulière avant le remplacement. Séparez chaque élément par une entrée.", "ext_regex_affects": "Affecte", "ext_regex_user_input": "Entrée de l'utilisateur", "ext_regex_ai_output": "<PERSON><PERSON><PERSON>", "Slash Commands": "Commandes barre oblique", "ext_regex_min_depth_desc": "Lorsqu'il est appliqué aux prompts ou à l'affichage, n'affecte que les messages d'au moins N niveaux de profondeur. 0 = dernier message, 1 = avant-dernier message, etc. Ne compte que les entrées WI @Depth et les messages utilisables, c'est-à-dire non cachés ou système.", "Min Depth": "Profondeur minimale", "ext_regex_min_depth_placeholder": "Illimité", "ext_regex_max_depth_desc": "Lorsqu'il est appliqué aux prompts ou à l'affichage, n'affecte que les messages en profondeur ne dépassant pas N niveaux. 0 = dernier message, 1 = avant-dernier message, etc. Ne compte que les entrées WI @Depth et les messages utilisables, c'est-à-dire non cachés ou système.", "ext_regex_other_options": "Autres options", "Only Format Display": "Format d'affichage uniquement", "ext_regex_only_format_prompt_desc": "L'historique des discussions ne changera pas, seule l'invite lors de l'envoi de la demande (lors de la génération).", "Only Format Prompt (?)": "Uniquement l'invite de formatage", "Run On Edit": "Exécuter sur Modifier", "ext_regex_substitute_regex_desc": "Remplacez {{macros}} dans Find Regex avant de l'exécuter", "ext_regex_import_target": "Importer vers :", "ext_regex_disable_script": "<PERSON><PERSON><PERSON><PERSON> le <PERSON>", "ext_regex_enable_script": "<PERSON><PERSON> le <PERSON>", "ext_regex_edit_script": "Modifier le script", "ext_regex_move_to_global": "Passer aux scripts globaux", "ext_regex_move_to_scoped": "Passer aux scripts étendus", "ext_regex_export_script": "Exporter le script", "ext_regex_delete_script": "Supprimer le script", "Trigger Stable Diffusion": "Déclencher Stable Diffusion", "sd_Yourself": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "sd_Your_Face": "Votre visage", "sd_Me": "<PERSON><PERSON>", "sd_The_Whole_Story": "Toute l'histoire", "sd_The_Last_Message": "<PERSON> message", "sd_Raw_Last_Message": "Dernier message brut", "sd_Background": "Arrière-plan", "Image Generation": "Génération d'images", "sd_refine_mode": "Autoriser la modification manuelle des prompts avant de les envoyer à l'API de génération", "sd_refine_mode_txt": "Modifier les prompts avant la génération", "sd_interactive_mode": "Générez automatiquement des images lors de l'envoi de messages tels que « envoyez-moi une photo de chat ».", "sd_interactive_mode_txt": "Mode interactif", "sd_multimodal_captioning": "Utilisez le sous-titrage multimodal pour générer des prompts pour les portraits des utilisateurs et des personnages en fonction de leurs avatars.", "sd_multimodal_captioning_txt": "Utiliser le sous-titrage multimodal pour les portraits", "sd_snap": "Requêtes de génération d'instantanés avec un rapport hauteur/largeur forcé (portraits, arrière-plans) à la résolution connue la plus proche, tout en essayant de préserver le nombre absolu de pixels (recommandé pour SDXL).", "sd_snap_txt": "Résolutions ajustées automatiquement", "Source": "Source", "sd_auto_url": "Exemple : {{auto_url}}", "Authentication (optional)": "Authentification (facultatif)", "Example: username:password": "Exemple : nom d'utilisateur : mot de passe", "Important:": "Important:", "sd_auto_auth_warning_1": "exécuter l'interface utilisateur Web SD avec le", "sd_auto_auth_warning_2": "drapeau! Le serveur doit être accessible depuis la machine hôte de SillyTavern.", "sd_drawthings_url": "Exemple : {{drawthings_url}}", "sd_drawthings_auth_txt": "exécutez l'application DrawThings avec le commutateur API HTTP activé dans l'interface utilisateur ! Le serveur doit être accessible depuis la machine hôte de SillyTavern.", "sd_vlad_url": "Exemple : {{vlad_url}}", "The server must be accessible from the SillyTavern host machine.": "Le serveur doit être accessible depuis la machine hôte de SillyTavern.", "Hint: Save an API key in AI Horde API settings to use it here.": "Astuce : enregistrez une clé API dans les paramètres de l'API AI Horde pour l'utiliser ici.", "Allow NSFW images from Horde": "Autoriser les images NSFW de la Horde", "Sanitize prompts (recommended)": "<PERSON><PERSON>in<PERSON>er les prompts (recommandé)", "Automatically adjust generation parameters to ensure free image generations.": "Ajustez automatiquement les paramètres de génération pour garantir des générations d’images gratuites.", "Avoid spending Anlas": "<PERSON><PERSON><PERSON><PERSON> <PERSON> Anlas", "Opus tier": "(Tier Opus)", "View my Anlas": "Voir mes Anlas", "These settings only apply to DALL-E 3": "Ces paramètres s'appliquent uniquement à DALL-E 3", "Image Style": "Style de l'image", "Image Quality": "Qualité de l'image", "Standard": "Standard", "HD": "HD", "sd_comfy_url": "Exemple : {{comfy_url}}", "Open workflow editor": "Ouv<PERSON>r l'éditeur de workflow", "Create new workflow": "Créer un nouveau workflow", "Delete workflow": "Supprimer le workflow", "Enhance": "<PERSON><PERSON><PERSON><PERSON>", "Decrisper": "Dé<PERSON>rypteur", "Sampling steps": "Étapes d'échantillonnage ()", "Width": "<PERSON><PERSON>", "Height": "<PERSON><PERSON>", "Resolution": "Résolution", "Model": "<PERSON><PERSON><PERSON><PERSON>", "Sampling method": "Méthode d'échantillonnage", "SMEA versions of samplers are modified to perform better at high resolution.": "Les versions SMEA des échantillonneurs sont modifiées pour mieux fonctionner à haute résolution.", "SMEA": "SDMEA", "DYN variants of SMEA samplers often lead to more varied output, but may fail at very high resolutions.": "Les variantes DYN des échantillonneurs SMEA conduisent souvent à des sorties plus variées, mais peuvent échouer à des résolutions très élevées.", "DYN": "DYN", "Scheduler": "Planificateur", "Restore Faces": "Restaurer les visages", "Hires. Fix": "Hires. Fix", "Upscaler": "Upscaleur", "Upscale by": "Upscale par", "Denoising strength": "Force de débruitage", "Hires steps (2nd pass)": "Nombre d'étapes Hires (2ème passage)", "Preset for prompt prefix and negative prompt": "Preset pour le préfixe de prompt et le prompt négatif", "Style": "Style", "Save style": "Enregistrer le style", "Delete style": "Supprimer le style", "Common prompt prefix": "Préfixe de prompt commun", "sd_prompt_prefix_placeholder": "Utilisez {prompt} pour spécifier où le prompt générée sera insérée", "Negative common prompt prefix": "Préfixe de prompt commun négatif", "Character-specific prompt prefix": "Préfixe de prompt spécifique au personnage", "Won't be used in groups.": "Ne sera pas utilisé en groupe.", "sd_character_prompt_placeholder": "Toutes les caractéristiques qui décrivent le personnage actuellement sélectionné. Sera ajouté après un préfixe de prompt commun.\nExemple : femme, yeux verts, cheveux bruns, chemise rose", "Character-specific negative prompt prefix": "Préfixe de prompt négatif spécifique au personnage", "sd_character_negative_prompt_placeholder": "Toutes les caractéristiques qui ne devraient pas apparaître pour le personnage sélectionné. Sera ajouté après un préfixe dde prompt commun négatif.\nExemple : bijoux, chaussures, lunettes", "Shareable": "Partageable", "Image Prompt Templates": "<PERSON><PERSON><PERSON><PERSON> de prompt d'image", "Translate files into English before processing": "Traduire les fichiers en anglais avant le traitement", "Manager Users": "gérer les utilisateurs", "New User": "Nouvel utilisateur", "Status:": "Statut:", "Created:": "Créé:", "Display Name:": "Afficher un nom:", "User Handle:": "Identifiant utilisateur :", "Password:": "Mot de passe:", "Confirm Password:": "Confirmez le mot de passe:", "This will create a new subfolder...": "<PERSON>la c<PERSON>era un nouveau sous-dossier dans le répertoire /data/ avec le handle de l'utilisateur comme nom de dossier.", "Current Password:": "Mot de passe actuel:", "New Password:": "Nouveau mot de passe:", "Confirm New Password:": "Confirmer le nouveau mot de passe:", "Execute": "Exécuter", "Are you sure you want to delete this user?": "Êtes-vous sûr de vouloir supprimer cet utilisateur ?", "Deleting:": "Suppression :", "Also wipe user data.": "Effacez également les données utilisateur.", "Warning:": "Avertissement:", "This action is irreversible.": "Cette action est irréversible.", "Type the user's handle below to confirm:": "<PERSON><PERSON><PERSON> le pseudo de l'utilisateur ci-dessous pour confirmer :", "Import Characters": "Importer des personnages", "Enter the URL of the content to import": "Saisissez l'URL du contenu à importer", "Supported sources:": "Sources prises en charge :", "char_import_1": "Personnage de Chub (lien direct ou identifiant)", "char_import_example": "Exemple:", "char_import_2": "Lorebook de Chub (lien direct ou ID)", "char_import_3": "Personnage de JanitorAI (lien direct ou UUID)", "char_import_4": "Personnage de Pygmalion.chat (lien direct ou UUID)", "char_import_5": "Personnage de AICharacterCards.com (lien direct ou identifiant)", "char_import_6": "Lien PNG direct (voir", "char_import_7": "pour les hôtes autorisés)", "char_import_8": "Personnage de RisuRealm (lien direct)", "char_import_9": "Personnage de Soulkyn (lien direct)", "char_import_10": "Personnage de Perchance (lien direct ou UUID + .gz)", "Supports importing multiple characters.": "Prend en charge l'importation de plusieurs caractères.", "Write each URL or ID into a new line.": "Écrivez chaque URL ou identifiant dans une nouvelle ligne.", "Export for character": "Exportation pour le personnage", "Export prompts for this character, including their order.": "Exportez les invites pour ce personnage, y compris leur ordre.", "Export all": "Exporter tout", "Export all your prompts to a file": "Exportez toutes vos invites dans un fichier", "Insert prompt": "Insérer une invitation", "Delete prompt": "Supprimer l'invitation", "Import a prompt list": "Importer une liste d'invitations", "Export this prompt list": "Exporter cette liste d'invitations", "Reset current character": "Réinitialiser le personnage actuel", "New prompt": "Nouvelle invitation", "Prompts": "Invitations", "Total Tokens:": "Tokens totaux :", "prompt_manager_tokens": "Tokens", "Are you sure you want to reset your settings to factory defaults?": "Êtes-vous sûr de vouloir réinitialiser vos paramètres aux valeurs d'usine par défaut ?", "Don't forget to save a snapshot of your settings before proceeding.": "N'oubliez pas d'enregistrer un instantané de vos paramètres avant de continuer.", "Settings Snapshots": "Paramètres des instantanés", "Record a snapshot of your current settings.": "Enregistrez un instantané de vos paramètres actuels.", "Make a Snapshot": "Faire un instantané", "Restore this snapshot": "Restaurer cet instantané", "Hi,": "Salut,", "To enable multi-account features, restart the SillyTavern server with": "Pour activer les fonctionnalités multi-comptes, red<PERSON><PERSON><PERSON> le serveur SillyTavern avec", "set to true in the config.yaml file.": "défini sur true dans le fichier config.yaml.", "Account Info": "Informations de compte", "To change your user avatar, use the buttons below or select a default persona in the Persona Management menu.": "Pour modifier votre avatar utilisateur, utilisez les boutons ci-dessous ou sélectionnez un personnage par défaut dans le menu Gestion des personnages.", "Set your custom avatar.": "Définissez votre avatar person<PERSON><PERSON>é.", "Remove your custom avatar.": "Supp<PERSON>ez votre avatar person<PERSON><PERSON><PERSON>.", "Handle:": "Poignée:", "This account is password protected.": "Ce compte est protégé par mot de passe.", "This account is not password protected.": "Ce compte n'est pas protégé par mot de passe.", "Account Actions": "Actions du compte", "Change Password": "Changer le mot de passe", "Manage your settings snapshots.": "<PERSON><PERSON><PERSON> vos instantanés de paramètres.", "Download a complete backup of your user data.": "Téléchargez une sauvegarde complète de vos données utilisateur.", "Download Backup": "Télécharger la sauvegarde", "Danger Zone": "Zone dangereuse", "Reset your settings to factory defaults.": "Réinitialisez vos paramètres aux valeurs par défaut d'usine.", "Reset Settings": "Réinitialiser les options", "Wipe all user data and reset your account to factory settings.": "Effacez toutes les données utilisateur et réinitialisez votre compte aux paramètres d'usine.", "Reset Everything": "<PERSON><PERSON> réinitialiser", "Reset Code:": "Code de réinitialisation :", "Want to update?": "<PERSON><PERSON> de mettre à jour ?", "How to start chatting?": "Comment commencer à discuter ?", "Click _space": "Cliquez sur", "and pick a character.": "et choisissez un personnage.", "Download Extensions & Assets": "Télécharger des extensions et des ressources", "menu within": "à l'intérieur de", "Confused or lost?": "Confus ou perdu ?", "click these icons!": "cliquez sur ces icônes !", "in the chat bar": "dans la barre de chat", "SillyTavern Documentation Site": "Documentation de SillyTavern", "Still have questions?": "Vous avez encore des questions ?", "Join the SillyTavern Discord": "<PERSON><PERSON><PERSON><PERSON> le Discord de SillyTavern", "Post a GitHub issue": "Poster un problème sur GitHub", "Contact the developers": "Contacter les développeurs", "Rename current preset": "Renommer le preset actuel", "Linear": "Linéaire", "Quad": "Quad", "Conf": "Conf", "Middle-out Transform": "Transformation Middle-out", "Auto": "Auto", "Allow": "Autoriser", "Forbid": "Interdire", "Auxiliary": "Auxiliaire", "Post-History Instructions": "Instructions post-histoire", "Unified Sampling": "Échantillonnage unifié", "Top nsigma": "Top nsigma", "Exclude Top Choices (XTC)": "Exclure les Top Choices (XTC)", "Threshold": "<PERSON><PERSON>", "A greedy, brute-force algorithm used in LLM sampling to find the most likely sequence of words or tokens. It expands multiple candidate sequences at once, maintaining a fixed number (beam width) of top sequences at each step.": "Un algorithme gourmand et brutal utilisé dans l'échantillonnage LLM pour trouver la séquence de mots ou de tokens la plus probable. Il développe plusieurs séquences candidates à la fois, en conservant un nombre fixe (largeur du faisceau) de séquences supérieures à chaque étape.", "# of Beams": "# de faisceaux", "The number of sequences generated at each step with Beam Search.": "Le nombre de séquences générées à chaque étape avec Beam Search.", "Penalize sequences based on their length.": "Pénaliser les séquences en fonction de leur longueur.", "Controls the stopping condition for beam search. If checked, the generation stops as soon as there are '# of Beams' sequences. If not checked, a heuristic is applied and the generation is stopped when it's very unlikely to find better candidates.": "Contrôle la condition d'arrêt de la recherche de faisceaux. Si cette case est cochée, la génération s'arrête dès qu'il y a « # of Beams » séquences. Si cette case n'est pas cochée, une heuristique est appliquée et la génération est arrêtée lorsqu'il est très peu probable de trouver de meilleurs candidats.", "Seed_desc": "Une graine aléatoire à utiliser pour obtenir des résultats déterministes et reproductibles. Définir à -1 pour utiliser une graine aléatoire.", "Sampler Order": "Ordre des échantillonneurs", "Aphrodite only. Determines the order of samplers. Skew is always applied post-softmax, so it's not included here.": "Aphrodite seulement. Determines l'ordres des échantillonneurs. L'obliquité est toujours appliquée après le softmax, elle n'est donc pas incluse ici.", "Aphrodite only. Determines the order of samplers.": "Aphrodite seulement. Determines l'ordres des échantillonneurs.", "character_names_none": "Ne jamais ajouter de préfixes de noms de personnages. Peut mal se comporter en groupe, à choisir avec précaution.", "Completion Object": "Completion Object", "enable_functions_desc_1": "Autorise l'utilisation", "enable_functions_desc_2": "outils de fonction", "enable_functions_desc_3": "Peut être utilisé par diverses extensions pour fournir des fonctionnalités supplémentaires.", "Request model reasoning": "<PERSON><PERSON><PERSON> les pensées du modèle", "Allows the model to return its thinking process.": "Permet au modèle de retourner son processus de réflexion.", "Confirm token parsing with": "Confirmer l'analyse des tokens avec", "openai_logit_bias_no_items": "Aucun élément", "api_no_connection": "Pas de connection...", "AI Horde Website": "Site Web de AI Horde", "Generic (OpenAI-compatible) [LM Studio, LiteLLM, etc.]": "Générique (OpenAI-compatible) [LM Studio, LiteLLM, etc.]", "Allow fallback providers": "Autoriser les fournisseurs de secours", "Model ID (optional)": "Model ID (optionnel)", "Featherless Model Selection": "Séléction de modèle Featherless", "category": "categorie", "Top": "Top", "All": "Tous", "class": "Toutes les classes", "No model description": "[Pas de dés<PERSON>]", "HuggingFace Token": "Token <PERSON>", "Endpoint URL": "URL Endpoint", "Example: https://****.endpoints.huggingface.cloud": "Exemple: https://****.endpoints.huggingface.cloud", "Tabby Model": "<PERSON><PERSON><PERSON><PERSON>", "must be set in Tabby's config.yml to switch models.": "doit être défini dans le fichier config.yml de Tabby pour changer de modèle.", "Use an admin API key.": "Utiliser une clé API d'administrateur.", "Derive context size from backend": "Déterminer la taille du contexte à partir du backend", "Custom (OpenAI-compatible)": "Personnalisé (OpenAI-compatible)", "Using a proxy that you're not running yourself is a risk to your data privacy.": "L'utilisation d'un proxy que vous ne gérez pas vous-même présente un risque pour la confidentialité de vos données.", "ANY support requests will be REFUSED if you are using a proxy.": "TOUTE demande d'assistance sera REFUSÉE si vous utilisez un proxy.", "Do not proceed if you do not agree to this!": "Ne continuez pas si vous n'êtes pas d'accord avec cela !", "Claude API Key": "Clé API Claude", "Allow fallback models": "Autoriser les modèles de secours", "NanoGPT API Key": "Clé API NanoGPT", "NanoGPT Model": "<PERSON><PERSON><PERSON><PERSON>", "DeepSeek API Key": "Clé API DeepSeek", "DeepSeek Model": "<PERSON><PERSON><PERSON><PERSON> DeepSeek", "Block Entropy API Key": "Clé API Block Entropy", "Select a Model": "Sélectionner un modèle", "Example: http://localhost:1234/v1": "Exemple: http://localhost:1234/v1", "(Optional)": "(Optionnel)", "Enter a Model ID": "Saisir un ID de modèle", "Example: gpt-3.5-turbo": "Exemple: gpt-3.5-turbo", "prompt_post_processing_none": "Aucun", "prompt_post_processing_merge": "Fusionner les rôles consécutifs", "prompt_post_processing_semi": "Semi-strict (rôles alternés)", "prompt_post_processing_strict": "Strict (l'utilisateur d'abord, rôles alternés)", "01.AI API Key": "01.Clé IA API", "01.AI Model": "01.<PERSON><PERSON><PERSON><PERSON>", "Additional Parameters": "Paramètres supplémentaires", "Import Advanced Formatting settings": "Importer les paramètres de formatage avancé\n\nVous pouvez également fournir des fichiers hérités pour les modèles Instruct et Context..", "Master Import": "Master Import", "Export Advanced Formatting settings": "Exporter les paramètres de formatage avancé", "Master Export": "Master Export", "context_derived": "Dériver des métadonnées du modèle, si possible.", "Select your current Context Template": "Sélectionnez votre modèle de contexte actuel", "Update current template": "Mise à jour du modèle actuel", "Rename current template": "Renommer le modèle actuel", "Save template as": "Enregistrer le modèle sous", "Import template": "Importer un modèle", "Export template": "Exporter un modèle", "Restore current template": "Rest<PERSON><PERSON> le modèle actuel", "Delete the template": "Supp<PERSON><PERSON> le modèle", "Disabling is not recommended.": "La désactivation n'est pas recommandée.", "Separators as Stop Strings": "Séparateurs comme chaînes d'arr<PERSON>", "Add Character and User names to a list of stopping strings.": "Ajouter les noms de personnages et d'utilisateurs à une liste de chaînes d'arrêt.", "Names as Stop Strings": "Noms comme chaînes d'<PERSON>", "context_allow_post_history_instructions": "Inclut les instructions post-historiques à la fin du prompt, si elles sont définies dans la fiche de personnage ET si l'option 'Préférer les instructions de personnage' est activée.\nN'EST PAS RECOMMANDÉ POUR LES MODÈLES DE COMPLÉTION DE TEXTE, CAR IL PEUT ENTRAÎNER DE MAUVAIS RÉSULTATS.", "Instruct Template": "<PERSON><PERSON><PERSON><PERSON> d'instruction", "instruct_derived": "Dériver des métadonnées du modèle, si possible.", "instruct_enabled": "Activer le mode d'instruction", "Select your current Instruct Template": "Sélectionnez votre modèle d'instruction actuel", "Delete template": "Supp<PERSON><PERSON> le modèle", "instruct_template_activation_regex_desc": "Lors de la connexion à une API ou du choix d'un modèle, ce modèle d'instruction est automatiquement activé si le nom du modèle correspond à l'expression régulière fournie.", "Never": "<PERSON><PERSON>", "Groups and Past Personas": "Groupes et personas antérieures", "Always": "Toujours", "Instruct Sequences": "Séquences d'instruction", "User Message Sequences": "Séquences de messages d'utilisateurs", "User Prefix": "Préfixe du message de l'utilisateur", "User Suffix": "Suffixe du message de l'utilisateur", "Assistant Message Sequences": "Séquences de messages de l'assistant", "Assistant Prefix": "Préfixe du message de l'assistant", "Assistant Suffix": "Suffixe du message de l'assistant", "System Message Sequences": "Séquences de messages système", "System Prefix": "Préfixe des messages système", "System Suffix": "Suffixe des messages du système", "System Prompt Sequences": "Séquences du prompt système", "Inserted before the first User's message.": "Inséré avant le premier message de l'utilisateur.", "First User Prefix": "Préfixe du premier utilisateur", "instruct_last_input_sequence": "Inséré avant le dernier message de l'utilisateur.", "Last User Prefix": "Préfixe du dernier utilisateur", "sysprompt_enabled": "Activer le prompt système", "Select your current System Prompt": "Sélectionnez votre prompt système actuel", "Update current prompt": "Mise à jour du prompt actuel", "Rename current prompt": "Renommer le prompt actuel", "Save prompt as": "Enregistrer le prompt sous", "Restore current prompt": "Restaurer le prompt actuel", "Prompt Content": "Contenu du prompt", "comma delimited,no spaces between": "délimité par des virgules, sans espace entre", "(disabled when max recursion steps are used)": "(désactivé lorsque le nombre maximum de pas de récursivité est utilisé)", "Cap the number of entry activation recursions": "Plafonner le nombre de récursions d'activation d'entrée", "Max Recursion Steps": "Nombre maximal d'étapes de récursivité", "0 = unlimited, 1 = scans once and doesn't recurse, 2 = scans once and recurses once, etc": "0 = illimité, 1 = scanne une fois et ne récure pas, 2 = scanne une fois et récure une fois, etc.\n(désactivé lorsque des activations minimales sont utilisées)", "Include names with each message into the context for scanning": "Inclure les noms dans chaque message dans le contexte pour l'analyse.", "Apply current sorting as Order": "Appliquer le tri actuel comme ordre", "Display swipe numbers for all messages, not just the last.": "Afficher le nombre de balayage sur tous les messages, et pas seulement le dernier.", "Swipe # for All Messages": "Balayage # pour tous les messages", "Defines on importing cards which action should be chosen for importing its listed tags. 'Ask' will always display the dialog.": "Dé<PERSON>it, lors de l'importation de cartes, l'action à choisir pour importer les tags répertoriés. L'action 'Demander' affichera toujours la boîte de dialogue.", "Ask": "<PERSON><PERSON><PERSON>", "tag_import_none": "Aucun", "tag_import_all": "Tous", "tag_import_existing": "Existant", "If checked and the character card contains a Post-History Instructions override, use that instead": "Si cette case est cochée et que la carte de personnage contient une instruction de remplacement pour le post-histoire, utilisez-la à la place.", "Prefer Character Card Instructions": "Préférer les instructions du personnage", "Enable auto-select of input text in some text fields when clicking/selecting them. Applies to popup input textboxes, and possible other custom input fields.": "Active la sélection automatique du texte saisi dans certains champs de texte lorsque l'on clique dessus ou qu'on les sélectionne. S'applique aux zones de texte de type popup et, éventuellement, à d'autres champs de saisie personnalisés.", "Auto-select Input Text": "Sélection automatique du texte d'entrée", "markdown_hotkeys_desc": "Activer les raccourcis clavier pour l'insertion de caractères au format markdown dans certaines zones de saisie de texte. Voir '/help hotkeys'.", "Markdown Hotkeys": "Markdown Hotkeys", "mui_reset": "Réinitialisation", "Show a button in the input area to ask the AI to impersonate your character for a single message": "Affichez un bouton dans la zone de saisie pour demander à l'IA de se faire passer pour votre personnage le temps d'un message.", "Quick 'Impersonate' button": "Bouton  \"Usurpation\" rapide", "In group chat, highlight the character(s) that are currently queued to generate responses and the order in which they will respond.": "Dans le chat de groupe, mettez en évidence le(s) personnage(s) qui sont actuellement en file d'attente pour générer des réponses et l'ordre dans lequel ils répondront.", "Show group chat queue": "Afficher la file d'attente du chat de groupe", "Keyboard": "Clavier:", "Select with Tab or Enter": "Sélectionner avec Tab ou Entrée", "Select with Tab": "Sélectionner avec Tab", "Select with Enter": "Sélectionner avec Entrée", "stscript_parser_flag_replace_getvar_label": "Empêche les macros {{getvar::}} {{getglobalvar::}} d'avoir des valeurs littérales de type macro auto-évaluées.\nPar exemple, \"{{newline}}\" reste la chaîne littérale \"{{newline}}\"\n\n((Cela se fait en remplaçant en interne les macros {{getvar::}} {{getglobalvar::}} par des variables scopées)", "Background Image": "Arrière-plans", "Background Fitting": "Ajustement de l'arrière-plan", "Classic": "Classique", "Cover": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Contain": "Contenir", "Stretch": "<PERSON><PERSON><PERSON>", "Center": "Centrer", "Persona Lore Alt+Click to open the lorebook": "Lore de persona\nAlt+Click pour ouvrir le lorebook", "None (disabled)": "Aucun (désactivé)", "world_button_title": "Lore de personnage\n\nCliquer pour charger\nMajuscule-clic pour ouvrir la fenêtre contextuelle « Lien vers le Wold Info ».", "Chat Lore Alt+Click to open the lorebook": "Lore du chat\nAlt+Click pour ouvrir le lorebook", "Manual": "<PERSON>", "Duplicate persona": "Dup<PERSON>r la persona", "popup-button-crop": "<PERSON><PERSON><PERSON><PERSON>", "Close popup": "<PERSON><PERSON><PERSON> le popup", "Close": "<PERSON><PERSON><PERSON>", "Any contents here will replace the default Post-History Instructions used for this character. (v2 spec: post_history_instructions)": "Tout contenu ici remplacera les instructions post-histore par défaut utilisées pour ce personnage.\n(v2 spec: post_history_instructions)", "WI Entry Status:🔵 Constant🟢 Normal🔗 Vectorized": "WI Statut de l'entrée :\r🔵 Constant\r🟢 Normal\r🔗 Vectorisé", "Use global": "Utiliser les données globales", "Whole Words": "<PERSON>ts entiers", "Group Scoring": "Notation des groupes", "delay_until_recursion_level": "Définit les niveaux de délai pour les balayages récursifs.\r\rDans un premier temps, seul le premier niveau (le plus petit nombre) sera pris en compte.\rSi aucune correspondance n'est trouvée, le niveau suivant devient éligible pour la correspondance.\rCette opération est répétée jusqu'à ce que tous les niveaux soient vérifiés.\r\rLié au réglage \"Délai avant récursion\".", "Recursion Level": "Niveau de récursivité", "Non-recursable (will not be activated by another)": "Non récursable (ne peut être activé par un autre)", "Delay until recursion (can only be activated on recursive checking)": "<PERSON><PERSON><PERSON> jusqu'à la récursion (ne peut être activé que lors d'un contrôle récursif)", "Prioritize": "Priorités", "Sticky entries will stay active for N messages after being triggered.": "Les entrées collantes restent actives pendant N messages après avoir été déclenchées.", "Sticky": "<PERSON>lant", "Entries with a cooldown can't be activated N messages after being triggered.": "Les entrées avec un temps de recharge ne peuvent pas être activées N messages après avoir été déclenchées.", "Cooldown": "Récupération", "Entries with a delay can't be activated until there are N messages present in the chat.": "Les entrées avec un délai ne peuvent pas être activées tant qu'il n'y a pas N messages dans le chat.", "Delay": "<PERSON><PERSON><PERSON>", "Filter to Characters or Tags": "Filtre sur les personnages ou les tags", "Switch the Character/Tags filter around to exclude the listed characters and tags from matching for this entry": "Changez le filtre Personnages/Tags pour exclure les personnages et tags listés de la correspondance pour cette entrée.", "Exclude": "Exclure", "Relative (to other prompts in prompt manager) or In-chat @ Depth.": "Relative (par rapport à d'autres prompts dans le gestionnaire de prompts) ou In-chat @ Depth.", "prompt_manager_in_chat": "In-chat", "The content of this prompt is pulled from elsewhere and cannot be edited here.": "Le contenu de ce message est tiré d'autres sources et ne peut être modifié ici..", "Open checkpoint chat\nShift+Click to replace the existing checkpoint with a new one": "Cliquer pour ouvrir le chat du point de contrôle\nShift+Click pour remplacer le point de contrôle existant par un nouveau.", "Caption": "Légende", "Swipe left": "Balayer à gauche", "Swipe right": "Balayer à droite", "alternate_greetings_hint_1": "Cliquez sur le", "alternate_greetings_hint_2": "bouton pour commencer !", "Alternate Greeting #": "Salutation alternative #", "Reroll with the entire prefix": "Renouvellement avec l'ensemble du préfixe", "Load a custom asset list or select": "Charger une liste de ressources personnalisée ou sélectionner", "to install 3rd party extensions.": "pour installer des extensions tierces.", "Assets URL": "URL des ressources", "load_asset_list_desc": "Chargement d'une liste d'extensions et de ressources sur la base d'un fichier de liste de ressources.\n\nL'URL des ressources par défaut dans ce champ pointe vers la liste des extensions et des ressources officielles.\nSi vous disposez d'une liste de ressources personnalisée, vous pouvez l'insérer ici.\n\nPour installer une seule extension tierce, utilisez le bouton \"Installer des extensions\" en haut à droite.", "Load an asset list": "Charger une liste de ressources", "Load Asset List": "Charger la liste des ressources", "Characters": "Personnages", "Disable": "Désactiver", "Enable": "Activer", "These files are available for the current character in all chats they are in.": "Ces fichiers sont disponibles pour le personnage actuel dans tous les chats auxquels il participe.", "These files are available for all characters in the current chat.": "Ces fichiers sont disponibles pour tous les personnages du chat actuel.", "Image Captioning": "Légende des images", "Local": "Local", "Multimodal (OpenAI / Anthropic / llama / Google)": "Multimodal (OpenAI / Anthropic / llama / Google)", "Extras": "Extras", "Horde": "Horde", "API": "API", "Text Generation WebUI (oobabooga)": "Text Generation WebUI (oobabooga)", "currently_selected": "[Actuellement sélectionné]", "currently_loaded": "[Actuellement chargée]", "Allow reverse proxy": "Autoriser le reverse proxy", "Hint:": "Indice:", "Set your API keys and endpoints in the 'API Connections' tab first.": "Définissez d'abord vos clés API et vos points de terminaison dans l'onglet « Connexions API ».", "Caption Prompt": "Légende Prompt", "Ask every time": "De<PERSON>er à chaque fois", "Message Template": "<PERSON><PERSON><PERSON><PERSON>", "(use _space": "(use", "macro)": "macro)", "Automatically caption images": "Légende automatique des images", "Edit captions before saving": "Modifier les légendes avant l'enregistrement", "Profile name:": "Nom du profil :", "Creating a Connection Profile": "Création d'un profil de connexion", "{{@key}}": "{{@key}}:", "Enter a name:": "Saisir un nom :", "Connection Profile": "Profil de connexion", "View connection profile details": "Voir les détails du profil de connexion", "Create a new connection profile": "Créer un nouveau profil de connexion", "Update a connection profile": "Mise à jour d'un profil de connexion", "Edit a connection profile": "Modifier un profil de connexion", "Reload a connection profile": "Recharger un profil de connexion", "Delete a connection profile": "Supprimer un profil de connexion", "Omitted Settings:": "Réglages omis :", "Character Expressions": "Expressions de personnages", "Translate text to English before classification": "Traduire le texte en anglais avant de le classer", "Show default images (emojis) if sprite missing": "Afficher les images par défaut (emojis) si le sprite est manquant", "Classifier API": "API de classification", "Select the API for classifying expressions.": "Sélectionnez l'API pour classer les expressions.", "Main API": "API principale", "WebLLM Extension": "Extension WebLLM", "LLM Prompt": "Prompt LLM", "Will be used if the API doesn't support JSON schemas or function calling.": "Sera utilisé si l'API ne prend pas en charge les schémas JSON ou les appels de fonction.", "Default / Fallback Expression": "Expression par défaut / de secours", "Set the default and fallback expression being used when no matching expression is found.": "Définir l'expression par défaut et l'expression de secours utilisées lorsqu'aucune expression correspondante n'est trouvée.", "Custom Expressions": "Expressions personnalisées", "Can be set manually or with an _space": "Peut être réglé manuellement ou à l'aide d'une", "space_ slash command.": "commande slash.", "Open a chat to see the character expressions.": "Ouvrez un chat pour voir les expressions du personnage.", "You are in offline mode. Click on the image below to set the expression.": "Vous êtes en mode hors ligne. Cliquez sur l'image ci-dessous pour définir l'expression.", "Sprite Folder Override": "Remplacement du dossier des sprites", "Use a forward slash to specify a subfolder. Example: _space": "Utilisez une barre oblique pour spécifier un sous-dossier. Exemple :", "Upload sprite pack (ZIP)": "Charger le pack de sprites (ZIP)", "Remove all image overrides": "Suppression de toutes les surcharges d'image", "Create new folder in the _space": "<PERSON><PERSON>er un nouveau dossier dans le", "folder of your user data directory and name it as the name of the character.": "de votre répertoire de données utilisateur et nommez-le comme le nom du personnage.", "Put images with expressions there. File names should follow the pattern:": "Placez-y des images avec des expressions. Les noms de fichiers doivent suivre le modèle :", "expression_label_pattern": "[expression_label].[image_format]", "Sprite set:": "Ensemble de sprites :", "Show Gallery": "Show Gallery", "ext_sum_title": "Résumer", "ext_sum_webllm": "Extension WebLLM", "ext_sum_restore_tip": "R<PERSON><PERSON><PERSON><PERSON>r un résumé précédent ; à utiliser plusieurs fois pour effacer l'état du résumé pour ce chat.", "ext_sum_force_tip": "<PERSON><PERSON><PERSON><PERSON><PERSON> une mise à jour du résumé dès maintenant.", "ext_sum_include_wi_scan_desc": "<PERSON><PERSON>re le dernier résumé dans l'analyse WI.", "ext_sum_include_wi_scan": "Inclure dans l'analyse des World Info", "None (not injected)": "Aucune (pas d'injection)", "ext_sum_injection_position_none": "Le résumé ne sera pas injecté dans le prompt. Vous pouvez toujours y accéder via la macro {{summary}}.", "Labels and Message": "Étiquettes et message", "Label": "Étiquette", "(label of the button, if no icon is chosen) ": "(étiquette du bouton, si aucune icône n'est choisie)", "Title": "Titre", "(tooltip, leave empty to show message or /command)": "(info-bulle, laisser vide pour afficher un message ou une commande)", "Message / Command:": "Message / Commande :", "Word wrap": "Enveloppe de mot", "Tab size:": "Tab size:", "Ctrl+Enter to execute": "Ctrl+Entrée pour exécuter", "Context Menu": "<PERSON><PERSON>", "Auto-Execute": "Auto-Exécution", "Don't trigger auto-execute": "Ne pas déclencher l'exécution automatique", "Invisible (auto-execute only)": "Invisible (exécution automatique uniquement)", "Execute on startup": "Exécuter au démarrage", "Execute on user message": "Exécuter sur message de l'utilisateur", "Execute on AI message": "Exécuter sur message de l'IA", "Execute on chat change": "Exécuter sur changement de chat", "Execute on new chat": "Exécution sur un nouveau chat", "Execute on group member draft": "Execute on group member draft", "Automation ID:": "Automation ID", "Testing": "<PERSON><PERSON><PERSON>", "Quick Reply": "Quick Reply", "Enable Quick Replies": "Activer les Quick Replies", "Combine Quick Replies": "Combiner les Quick Replies", "Show Popout Button": "Afficher le bouton contextuel (sur le bureau)", "Global Quick Reply Sets": "Global Quick Reply Sets", "Chat Quick Reply Sets": "<PERSON><PERSON> Quick Reply Sets", "Edit Quick Replies": "Editer les Quick Replies", "Disable Send (Insert Into Input Field)": "Désactiver l'envoi (insérer dans le champ de saisie)", "Place Quick Reply Before Input": "Placez le quick reply avant la saisie", "Inject user input automatically": "Injecter automatiquement les données de l'utilisateur", "(if disabled, use ": "(si d<PERSON><PERSON><PERSON><PERSON>, utiliser", "macro for manual injection)": "macro pour injection manuelle)", "Color": "<PERSON><PERSON><PERSON>", "Only apply color as accent": "N'appliquez la couleur qu'en tant qu'accent", "ext_regex_new_global_script_desc": "Nouveau script global de regex", "ext_regex_new_scoped_script_desc": "Nouveau script délimitée de regex", "ext_regex_disallow_scoped": "Interdire l'utilisation de regex délimitée", "ext_regex_allow_scoped": "Autoriser l'utilisation de regex délimitées", "ext_regex_user_input_desc": "Messages envoyés par l'utilisateur.", "ext_regex_ai_input_desc": "Messages reçus de l'API Génération.", "ext_regex_slash_desc": "Messages envoyés à l'aide de commandes STscript.", "ext_regex_wi_desc": "Contenu de l'entrée Lorebook/World Info. Nécessite de cocher la case 'Only Format Prompt' !", "ext_regex_run_on_edit_desc": "Exécute le script regex lorsque le message appartenant à un ou plusieurs rôles spécifiés est modifié.", "Macro in Find Regex": "Macros dans Find Regex", "Don't substitute": "Ne pas substituer", "Substitute (raw)": "Substituer (raw)", "Substitute (escaped)": "<PERSON><PERSON><PERSON><PERSON> (escaped)", "Ephemerality": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "ext_regex_only_format_visual_desc": "Le contenu du fichier d'historique du chat ne changera pas, mais les regex seront appliquées aux messages affichés dans l'UI du chat.", "Abort current image generation task": "Abandonner la tâche de génération d'images en cours", "Stop Image Generation": "Arrêt de la génération d'images", "sd_function_tool": "Utilisez les outils pour détecter automatiquement les intentions de générer des images.", "sd_function_tool_txt": "Utilisez les outils", "sd_free_extend": "Étendre automatiquement les prompts de sujet en mode libre (pas les portraits ou les arrière-plans) à l'aide d'un LLM actuellement sélectionné.", "sd_free_extend_txt": "Étendre les prompts de sujet en mode libre", "sd_free_extend_small": "(interactif/commandes)", "Model ID": "Model ID", "e.g. black-forest-labs/FLUX.1-dev": "ex. black-forest-labs/FLUX.1-dev", "API Key": "Clé API", "Click to set": "Cliquez pour définir", "You can find your API key in the Stability AI dashboard.": "Vous trouverez votre clé API dans le tableau de bord de Stability AI.", "Style Preset": "Preset de style", "Prompt Upsampling": "Prompt Upsampling", "Swap width and height": "Intervertir la largeur et la hauteur", "CLIP Skip": "CLIP Skip", "Karras": "<PERSON><PERSON><PERSON>", "Not all samplers supported.": "Tous les échantillonneurs ne sont pas pris en charge.", "sd_adetailer_face": "Utiliser ADetailer avec le modèle de visage pendant la génération. L'extension ADetailer doit être installée sur le backend.", "Use ADetailer (Face)": "Util<PERSON> (Face)", "(-1 for random)": "(-1 pour aléatoire)", "Chat Message Visibility (by source)": "Visibilité des messages de chat (par source)", "Uncheck to hide the extension's messages in chat prompts.": "Décochez cette case pour masquer les messages de l'extension dans les prompts de chat.", "Extensions Menu": "Menu des extensions", "Slash Command": "Commande Slash", "Interactive Mode": "Mode interactif", "Function Tool": "Outils", "ext_translate_btn_chat": "<PERSON><PERSON><PERSON><PERSON> le chat", "ext_translate_btn_input": "<PERSON>radui<PERSON> la saisie", "ext_translate_delete_confirm_1": "Êtes-vous sûr ?", "ext_translate_delete_confirm_2": "Cette action supprime le texte traduit de tous les messages de la discussion en cours. Cette action ne peut pas être annulée.", "ext_translate_title": "Traduction du chat", "ext_translate_auto_mode": "Mode auto", "ext_translate_mode_none": "Aucun", "ext_translate_mode_responses": "Traduire les réponses", "ext_translate_mode_inputs": "<PERSON>radui<PERSON> la saisie", "ext_translate_mode_both": "Traduire les deux", "ext_translate_mode_provider": "Fournisseur", "ext_translate_target_lang": "Langue cible", "ext_translate_clear": "Clear Translations", "Select TTS Provider": "Sélectionner le fournisseur TTS", "tts_enabled": "Activé", "Narrate user messages": "Narrer les messages de l'utilisateur", "Auto Generation": "Generation Auto", "Requires auto generation to be enabled.": "La génération automatique doit être activée.", "Narrate by paragraphs (when streaming)": "Narration par paragraphes (en cas de diffusion en continu)", "Only narrate quotes": "Ne narrer que les \"citations\"", "Ignore text, even quotes, inside asterisk": "Ignorer le *texte, même les \"citations\", à l'intérieur d'astérisques*", "Narrate only the translated text": "Ne narrer que le texte traduit", "Skip codeblocks": "Sauter les blocs de code", "Skip tagged blocks": "Skip <tagged> blocks", "Pass Asterisks to TTS Engine": "Transmettre les astérisques au moteur TTS", "Audio Playback Speed": "Vitesse de lecture audio", "Vector Storage": "Vector Storage", "Vectorization Source": "Source de vectorisation", "Local (Transformers)": "Local (Transformers)", "Vectorization Model": "Modèle de vectorisation", "Keep model in memory": "<PERSON><PERSON><PERSON> le modèle en mémoire", "Hint: Set the URL in the API connection settings.": "Conseil : définissez l'URL dans les paramètres de connexion à l'API.", "The server MUST be started with the --embedding flag to use this feature!": "Le serveur DOIT être démarré avec l'option --embedding pour utiliser cette fonctionnalité !", "NomicAI API Key": "Clé API NomicAI", "Query messages": "Messages de requête", "Score threshold": "<PERSON><PERSON>", "Chunk boundary": "Limite des morceaux", "World Info settings": "Réglages desWorld Info", "Enable for World Info": "Activer pour les World Info", "Enabled for all entries": "Activé pour toutes les entrées", "Checked: all entries except ❌ status can be activated.": "Vérifié : toutes les entrées sauf l'état ❌ peuvent être activées..", "Unchecked: only entries with ❌ status can be activated.": "Non coché : seules les entrées ayant un statut 🔗 peuvent être activées.", "Max Entries": "Nombre maximal d'entrées", "File vectorization settings": "Paramètres de vectorisation des fichiers", "Enable for files": "Activer pour les fichiers", "Message attachments": "Pièces jointes aux messages", "Size threshold (KB)": "<PERSON><PERSON>le (KB)", "Chunk size (chars)": "<PERSON><PERSON> des morceaux (chars)", "Chunk overlap (%)": "Chevauchement des morceaux (%)", "Retrieve chunks": "Récupérer des morceaux", "Data Bank files": "Fichiers de la banque de données", "Injection Template": "<PERSON><PERSON><PERSON><PERSON> d'<PERSON>", "Injection Position": "Position d'injection", "Vectorize All": "Vectoriser tout", "Purge Vectors": "Vecteurs de purge", "Chat vectorization settings": "Paramètres de vectorisation du chat", "Enabled for chat messages": "Activé pour les messages de chat", "Retain#": "Conserver#", "Insert#": "Insérer#", "Vector Summarization": "Résumés vectoriels", "Summarize chat messages for vector generation": "Résumer les messages de chat pour la génération de vecteurs", "Warning: This will slow down vector generation drastically, as all messages have to be summarized first.": "Attention : <PERSON><PERSON> considérablement la génération de vecteurs, car tous les messages doivent d'abord être résumés.", "Summarize chat messages when sending": "Résumer les messages de chat lors de l'envoi", "Warning: This might cause your sent messages to take a bit to process and slow down response time.": "Attention : Le traitement des messages envoyés risque d'être un peu plus long et de ralentir le temps de réponse.", "Extras API": "Extras API", "Only used when Main API or WebLLM Extension is selected.": "Utilisé uniquement lorsque l'API principale ou l'extension WebLLM est sélectionnée.", "Old messages are vectorized gradually as you chat. To process all previous messages, click the button below.": "Les anciens messages sont vectorisés progressivement au fur et à mesure que vous discutez.\n                    To process all previous messages, click the button below.", "View Stats": "Voir les statistiques", "Note:": "Note:", "this chat is temporary and will be deleted as soon as you leave it.": "ce chat est temporaire et sera supprimé dès que vous le quitterez.", "Enter a new display name:": "Saisissez un nouveau nom d'affichage :", "Import Tags For _begin": "Importer des tags pour ", "Click remove on any tag to remove it from this import.<br />Select one of the import options to finish importing the tags.": "Cliquez sur le bouton « Supprimer » d'un tag pour le retirer de l'importation..\n        Sélectionnez l'une des options d'importation pour terminer l'importation des tags.", "Existing Tags": "Tags existantes", "New Tags": "Nouveaux tags", "Folder Tags": "Tags de dossier", "The following tags will be auto-imported based on the currently selected folders": "Les tags suivants seront importés automatiquement en fonction des dossiers sélectionnés.", "Import None": "Aucun Import", "Import All": "Importer Tout", "Import Existing": "Importer l'existants", "Import": "Importer", "chat_rename_1": "Saisis<PERSON>z le nouveau nom du chat :", "chat_rename_2": "!!L'utilisation d'un nom de fichier existant produira une erreur !!!", "chat_rename_3": "<PERSON><PERSON> rompra le lien entre les points de contrôle des chats .", "chat_rename_4": "Il n'est pas nécessaire d'ajouter '.jsonl' à la fin.", "Enter Checkpoint Name:": "Saisir le nom du point de contrôle :", "(Leave empty to auto-generate)": "(Laisser vide pour une génération automatique)", "The currently existing checkpoint will be unlinked and replaced with the new checkpoint, but can still be found in the Chat Management.": "Le point de contrôle existant sera dissocié et remplacé par le nouveau point de contrôle, mais pourra toujours être trouvé dans la gestion du chat.", "Include Body Parameters": "Inclure les paramètres du corps", "custom_include_body_desc": "Paramètres à inclure dans le corps de la requête Chat Completion (objet YAML)\n\nExemple:\n- top_k: 20\n- repetition_penalty: 1.1", "Exclude Body Parameters": "Exclure les paramètres du corps", "custom_exclude_body_desc": "Paramètres à exclure du corps de la requête Chat Completion (tableau YAML)\n\nExemple:\n- frequency_penalty\n- presence_penalty", "Include Request Headers": "<PERSON><PERSON><PERSON> les en-têtes des requêtes", "custom_include_headers_desc": "En-têtes supplémentaires pour les demandes de Chat Completion (objet YAML)\n\nExemple:\n- CustomHeader: custom-value\n- AnotherHeader: custom-value", "Functions in this category are for advanced users only. Don't click anything if you're not sure about the consequences.": "Les fonctions de cette catégorie sont réservées aux utilisateurs avancés. Ne cliquez sur rien si vous n'êtes pas sûr des conséquences.", "THIS IS PERMANENT!": "CECI EST PERMANENT !", "Also delete the chat files": "Supprimez également les fichiers de chat", "Are you sure you want to duplicate this character?": "Êtes-vous sûr de vouloir dupliquer ce personnage ?", "If you just want to start a new chat with the same character...": "Si vous voulez simplement commencer un nouveau chat avec le même personnage, utilisez l'option \"Commencer un nouveau chat\" dans le menu d'options en bas à gauche.", "forbid_media_global_state_forbidden": "(interdit)", "forbid_media_global_state_allowed": "(autorisé)", "help_format_1": "Commandes de mise en forme du texte :", "help_format_2": "*texte*", "help_format_3": "s'affiche comme", "help_format_4": "italique", "help_format_5": "**texte**", "help_format_6": "s'affiche comme", "help_format_7": "gras", "help_format_8": "***text***", "help_format_9": "s'affiche comme", "help_format_10": "gras italique", "help_format_11": "__texte__", "help_format_12": "s'affiche comme", "help_format_13": "<PERSON><PERSON><PERSON>", "help_format_14": "~~texte~~", "help_format_15": "displays as a", "help_format_16": "strikethough", "help_format_17": "[texte](url)", "help_format_18": "s'affiche comme un", "help_format_19": "hyperlink", "help_format_20": "![texte](url)", "help_format_21": "s'affiche sous forme d'image", "help_format_22": "```texte```", "help_format_23": "s'affiche comme un bloc de code (de nouvelles lignes sont autorisées entre les backticks)", "help_format_like_this": "comme cela", "help_format_24": "`texte`", "help_format_25": "s'affiche comme du", "help_format_26": "code inline", "help_format_27": "> texte", "help_format_28": "s'affiche comme une citation en bloc (notez l'espace après >)", "help_format_29": "# texte", "help_format_30": "s'affiche comme un grand en-tête (notez l'espace)", "help_format_32": "## texte", "help_format_33": "s'affiche comme un en-tête moyen (notez l'espace)", "help_format_35": "### texte", "help_format_36": "s'affiche comme un petit en-tête (notez l'espace)", "help_1": "Bonjour à tous ! Veuillez sélectionner la rubrique d'aide sur laquelle vous souhaitez en savoir plus :", "help_2": "Commands Slash", "help_or": "ou", "help_3": "Formatage", "help_4": "<PERSON><PERSON><PERSON><PERSON>", "help_5": "{{<PERSON><PERSON>}}", "help_6": "Vous avez encore des questions ? Le", "help_7": "la documentation officielle de SillyTavern", "help_8": "contient beaucoup plus d'informations !", "help_hotkeys_0": "<PERSON><PERSON><PERSON><PERSON> chat", "help_hotkeys_1": "<PERSON><PERSON>", "help_hotkeys_2": "Modifier le dernier message dans le chat", "help_hotkeys_3": "Ctrl+Haut", "help_hotkeys_4": "Modifier le dernier message de l'utilisateur dans le chat", "help_hotkeys_5": "G<PERSON><PERSON>", "help_hotkeys_6": "balayage gauche", "help_hotkeys_7": "<PERSON><PERSON><PERSON>", "help_hotkeys_8": "balayage droite (REMARQUE : les touches de raccourci sont désactivées lorsque quelque chose est tapé dans la barre de discussion.)", "help_hotkeys_9": "Entrée", "help_hotkeys_10": "(avec la barre de chat sélectionnée)", "help_hotkeys_10_1": "envoyez votre message à l'IA", "help_hotkeys_11": "Ctrl+Entrée", "help_hotkeys_12": "Régénérer la dernière réponse de l'IA", "help_hotkeys_13": "Alt+Entrée", "help_hotkeys_14": "Poursuivre la dernière réponse de l'IA", "help_hotkeys_15": "Echap", "help_hotkeys_16": "arrêter la génération de réponses de l'IA, fermer les panneaux de l'interface utilisateur, annuler l'édition du message", "help_hotkeys_17": "Ctrl+Maj+<PERSON>ut", "help_hotkeys_18": "Dé<PERSON>ler jusqu'à la ligne de contexte", "help_hotkeys_19": "Ctrl+Maj+Bas", "help_hotkeys_20": "<PERSON><PERSON><PERSON><PERSON>", "help_hotkeys_21": "Fonctionne dans la barre de chat et les zones de texte marquées par cette icône :", "help_hotkeys_22": "**gras**", "help_hotkeys_23": "*italique*", "help_hotkeys_24": "__underline__", "help_hotkeys_25": "`code inline`", "help_hotkeys_26": "~~strikethrough~~", "Enter the Git URL of the extension to install": "Saisir l'URL Git de l'extension à installer", "Disclaimer:": "Disclaimer:", "Please be aware that using external extensions can have unintended side effects and may pose security risks. Always make sure you trust the source before importing an extension. We are not responsible for any damage caused by third-party extensions.": "Veuillez noter que l'utilisation d'extensions externes peut avoir des effets secondaires inattendus et présenter des risques pour la sécurité. Assurez-vous toujours de la fiabilité de la source avant d'importer une extension. Nous ne sommes pas responsables des dommages causés par des extensions tierces.", "Prompt Itemization": "Prompt Itemization", "Show Raw Prompt": "A<PERSON>icher le prompt brut", "Copy Prompt": "<PERSON><PERSON><PERSON> le prompt", "Show Prompt Differences": "Mont<PERSON> les différences entre les prompts", "API/Model:": "API/Modèle:", "Preset:": "Preset:", "Tokenizer:": "Tokenizer:", "Only the white numbers really matter. All numbers are estimates. Grey color items may not have been included in the context due to certain prompt format settings.": "Seuls les chiffres blancs comptent vraiment. Tous les chiffres sont des estimations.\n    Les éléments de couleur grise peuvent ne pas avoir été inclus dans le contexte en raison de certains paramètres du format du prompt.", "System Info:": "Info système :", "Prompt Tokens:": "Prompt Tokens:", "World Info:": "World Info:", "Chat History:": "Historique du chat :", "Extensions:": "Extensions:", "Bias:": "Biais:", "Total Tokens in Prompt:": "Total des Tokens dans le Prompt:", "Max Context": "Contexte Maximums", "(Context Size - Response Length)": "(<PERSON><PERSON> contexte - <PERSON><PERSON><PERSON> de la réponse)", "System-wide Replacement Macros (in order of evaluation):": "Macros de remplacement globales (par ordre d'évaluation) :", "help_macros_1": "uniquement pour la mise en lot des commandes slash. Remplacé par le résultat de la commande précédente.", "help_macros_2": "insère simplement une nouvelle ligne.", "help_macros_3": "supprime les nouvelles lignes entourant cette macro.", "help_macros_4": "pas d'opération, juste une chaîne vide.", "help_macros_5": "prompts globaux définis dans les paramètres de l'API. Valable uniquement dans les overrides de définitions avancées.", "help_macros_6": "l'entrée de l'utilisateur", "help_macros_7": "l'override du prompt principal du personnage", "help_macros_8": "les overrides des instructions post-histoire du personnage", "help_macros_9": "la description du personnage", "help_macros_10": "la personnalité du personnage", "help_macros_11": "le scénario du personnage", "help_macros_12": "la description de votre persona actuelle", "help_macros_13": "Exemples de dialogue du personnage", "help_macros_14": "Exemples de dialogues non formatés", "(only for Story String)": "(uniquement pour Story String)", "help_macros_summary": "le dernier résumé du chat généré par l'extension \"Summarize\" (si disponible).", "help_macros_15": "votre nom de persona actuel", "help_macros_16": "le nom du personnage", "help_macros_17": "le numéro de version du personnage", "help_macros_18": "une liste de noms de membres du groupe séparés par des virgules (y compris les personnes en sourdine) ou le nom du personnage dans les discussions en solo. Alias: {{charIfNotGroup}}", "help_groupNotMuted": "la même chose que {{group}}, mais exclut les membres en sourdine", "help_macros_19": "un nom de modèle de génération de texte pour l'API actuellement sélectionnée.", "Can be inaccurate!": "Peut être imprécis !", "help_macros_20": "le texte du dernier message de chat.", "help_macros_lastUser": "le texte du dernier message de chat de l'utilisateur.", "help_macros_lastChar": "le texte du dernier message de chat du personnage.", "help_macros_21": "index # du dernier message de chat. Utile pour la mise en lot des commandes slash.", "help_macros_22": "l'ID du premier message inclus dans le contexte. La génération doit être exécutée au moins une fois dans la session en cours.", "help_macros_23": "l'ID basé sur 1 du swipe actuel dans le dernier message de chat. Chaîne vide si le dernier message est caché par l'utilisateur ou le prompt.", "help_macros_24": "le nombre de swipes dans le dernier message de chat. Chaîne vide si le dernier message est caché par l'utilisateur ou le prompt.", "help_macros_reverse": "inverse le contenu de la macro.", "help_macros_25": "vous pouvez laisser une note ici, et la macro sera remplacée par un contenu vide. Non visible pour l'IA.", "help_macros_26": "l'heure actuelle", "help_macros_27": "la date du jour", "help_macros_28": "le jour de la semaine", "help_macros_29": "l'heure ISO actuelle (24 heures)", "help_macros_30": "la date ISO actuelle (AAAA-MM-JJ)", "help_macros_31": "la date et l'heure actuelles dans le format spécifié, par exemple pour la date/heure allemandes :", "help_macros_32": "l'heure actuelle dans le fuseau horaire UTC spécifié, ex. UTC-4 or UTC+2", "help_macros_33": "la différence de temps entre time1 et time2. Accepte les macros d'heure et de date. (Ex: {{timeDiff::{{isodate}} {{time}}::2024/5/11 12:30:00}})", "help_macros_34": "le temps écoulé depuis l'envoi du dernier message de l'utilisateur", "help_macros_35": "définit un biais comportemental pour l'IA jusqu'à la prochaine entrée de l'utilisateur. Les guillemets autour du texte sont importants.", "help_macros_36": "lance un dé. (ex :", "space_  will roll a 6-sided dice and return a number between 1 and 6)": "lancera un dé à 6 faces et renverra un nombre entre 1 et 6)", "help_macros_37": "renvoie un élément aléatoire de la liste. (ex :", "space_  will return 1 of the 4 numbers at random. Works with text lists too.": "renverra 1 des 4 nombres au hasard. Fonctionne également avec les listes de texte.", "help_macros_38": "syntaxe alternative pour random qui permet d'utiliser des virgules dans les éléments de la liste.", "help_macros_39": "choisit un élément aléatoire dans la liste. Fonctionne de la même manière que {{random}}, avec les mêmes options syntaxiques possibles, mais le choix restera cohérent pour ce chat une fois choisi et ne sera pas relancé sur les messages consécutifs et le traitement des prompts.", "help_macros_40": "ajoute dynamiquement du texte entre guillemets aux séquences de mots interdits, si le backend Text Generation WebUI est utilisé. Ne fait rien pour les autres backends. Peut être utilisé n'importe où (description de personnage, WI, AN, etc.) Les guillemets autour du texte sont importants.", "help_macros_isMobile": "\"true\" si ST fonctionne actuellement dans un environnement mobile, \"false\" sinon", "Instruct Mode and Context Template Macros:": "Mode d'instruction et macros de modèle de contexte :", "(enabled in the Advanced Formatting settings)": "(activée dans les paramètres de formatage avancés)", "help_macros_41": "longueur maximale autorisée du prompt en tokens = (taille du contexte - longueur de la réponse)", "help_macros_42": "contexte modèle exemple dialogues séparateur", "help_macros_43": "contexte modèle chat ligne de début", "help_macros_44": "contenu du prompt système s'il est activé (soit override du prompt du personnage si c'est autorisée, soit defaultSystemPrompt)", "help_macros_45": "contenu du prompt système", "help_macros_46": "séquence de préfixes du prompt système d'instruction", "help_macros_47": "séquence de suffixes du prompt système d'instruction", "help_macros_48": "séquence de préfixes de l'utilisateur d'instruction", "help_macros_49": "séquence de suffixes de l'utilisateur d'instruction", "help_macros_50": "séquence de préfixes de l'assistant d'instruction", "help_macros_51": "séquence de suffixes de l'assistant d'instruction", "help_macros_52": "instruct assistant first output sequence", "help_macros_53": "instruct assistant last output sequence", "help_macros_54": "instruct system message prefix sequence", "help_macros_55": "instruct system message suffix sequence", "help_macros_56": "instruct system instruction prefix", "help_macros_57": "instruct first user message filler", "help_macros_58": "instruct stop sequence", "help_macros_first_user": "instruct user first input sequence", "help_macros_last_user": "instruct user last input sequence", "Chat variables Macros:": "Macros de variables de chat :", "Local variables = unique to the current chat": "Variables locales = propres au chat actuel", "Global variables = works in any chat for any character": "Variables globales = fonctionnent dans n'importe quel chat pour n'importe quel personnage", "Scoped variables = works in STscript": "Variables scopés = fonctionne dans STscript", "help_macros_59": "remplacée par la valeur de la variable locale \"name\"", "help_macros_60": "remplacée par une chaîne vide, définit la variable locale \"name\" à \"value\"", "help_macros_61": "remplacés par des chaînes vides, ajoute une valeur numérique de \"increment\" à la variable locale \"name\"", "help_macros_62": "remplacé par le résultat de l'incrémentation de la valeur de la variable \"name\" de 1", "help_macros_63": "remplacé par le résultat de la décrémentation de la valeur de la variable \"name\" de 1", "help_macros_64": "remplacée par la valeur de la variable globale \"name\"", "help_macros_65": "remplacée par une chaîne vide, définit la variable globale \"name\" à \"value\"", "help_macros_66": "remplacé par une chaîne vide, ajoute une valeur numérique de \"increment\" à la variable globale \"name\"", "help_macros_67": "remplacé par le résultat de l'incrémentation de la valeur de la variable globale \"name\" de 1", "help_macros_68": "remplacé par le résultat de la décrémentation de la valeur de la variable globale \"name\" de 1", "help_macros_69": "remplacée par la valeur de la variable scopée \"name\"", "help_macros_70": "remplacée par la valeur de l'élément à l'index (pour les tableaux / listes ou objets / dictionnaires) de la variable scopée \"name\"", "Choose what to export": "Choisir ce qui doit être exporté", "{{name}}": "{{name}}", "Choose what to import": "Choisir ce qu'il faut importer", "If necessary, you can later restore this chat file from the /backups folder": "<PERSON> nécessaire, vous pouvez restaurer ultérieurement ce fichier de chat à partir du dossier /backups", "Also delete the current chat file": "Supprime également le fichier de chat actuel", "Persona Lorebook for": "Persona Lorebook pour", "persona_world_template_txt": "Une Wolrd Info sélectionnée sera liée à ce personnage. Lors de la création d'une réponse de l'IA,\n            il sera combiné avec les entrées des lorebooks globaux, de personnages et de chat.", "Are you sure you want to connect to the following proxy URL?": "Êtes-vous sûr de vouloir vous connecter à l'URL proxy suivante ?", "Encountered an error while processing your request.": "Une erreur s'est produite lors du traitement de votre requête.", "Check you have credits available on your": "Vérifiez que vous avez des crédits disponibles sur votre", "OpenAI account quora_error": "compte OpenAI", "dot quota_error": ".", "If you have sufficient credits, please try again later.": "Si vous avez suffisamment de crédits, ve<PERSON><PERSON><PERSON> réessayer plus tard.", "Enter your password below to confirm:": "Entrez votre mot de passe ci-dessous pour confirmer :", "Unique to this chat.": "Unique à ce chat.", "All group members will use the following scenario text instead of what is specified in their character cards.": "Tous les membres du groupe utiliseront le texte du scénario suivant au lieu de ce qui est spécifié dans leur fiche de personnage.", "The following scenario text will be used instead of the value set in the character card.": "Le texte du scénario suivant sera utilisé à la place de la valeur définie dans la fiche de personnage.", "Checkpoints inherit the scenario override from their parent, and can be changed individually after that.": "Les points de contrôle héritent du scénario de leur parent et peuvent ensuite être modifiés individuellement.", "Download Model": "Télécharger le modèle", "Downloader Options": "Options de téléchargement", "Extra parameters for downloading/HuggingFace API": "Paramètres supplémentaires pour le téléchargement/HuggingFace API.\rSi vous n'êtes pas sûr, laissez ces paramètres vides.", "Revision": "Révision", "Folder Name": "Nom du dossier de sortie", "HF Token": "Token HF", "Include Patterns": "Inclure les patterns", "Glob patterns of files to include in the download.": "Patterns globaux de fichiers à inclure dans le téléchargement.\rSéparer chaque pattern par une nouvelle ligne.", "Exclude Patterns": "Exclure les patterns", "Glob patterns of files to exclude in the download.": "Patterns globaux de fichiers à exclure du téléchargement.\rSéparer chaque pattern par une nouvelle ligne.", "Tag Management": "Gestion des tags", "Save your tags to a file": "Enregistrer vos tags dans un fichier", "Restore tags from a file": "Enregistrer vos tags dans un fichier", "Create a new tag": "C<PERSON>er un nouveau tag", "Drag handle to reorder. Click name to rename. Click color to change display.": "Faites glisser la poignée pour réorganiser. Cliquez sur le nom pour le renommer. Cliquez sur la couleur pour modifier l'affichage.", "Click on the folder icon to use this tag as a folder.": "Cliquez sur l'icône de dossier pour utiliser ce tag comme dossier.", "Use alphabetical sorting": "Utiliser le tri alphabétique", "tags_sorting_desc": "Si cette option est activée, les tags seront automatiquement triés par ordre alphabétique lors de leur création ou de leur renommage.\nSi cette option est désactivée, les nouveaux tags seront ajoutés à la fin.\n\nSi un tag est réorganisé manuellement en le faisant glisser, le tri automatique sera désactivé.", "Are you sure you want to delete the theme?": "Êtes-vous sûr de vouloir supprimer le thème ?", "This will delete all your settings and data. There will be no undo button. Make sure you have a backup before proceeding.": "Cette opération effacera tous vos paramètres et toutes vos données. Il n'y aura pas de bouton d'annulation.\n        Assurez-vous de disposer d'une sauvegarde avant de poursuivre.", "Account reset code has been posted to the server console.": "Le code de réinitialisation du compte a été affiché sur la console du serveur.", "and connect to an": "et se connectez à une", "You can add more": "<PERSON>ous pouvez ajouter d'autres", "or_welcome": "ou", "from other websites": "d'autres sites web.", "Go to the": "Aller au menu ", "to install additional features.": "pour installer des fonctionnalités supplémentaires.", "If you're connected to an API, try asking me something!": "Si vous êtes connecté à une API, essayez de me demander quelque chose !", "Title/Memo": "Titre/Memo", "Strategy": "Stratégie", "Position": "Position", "Trigger %": "Déclencheur %", "Only chunk on custom boundary": "Only chunk on custom boundary", "Generate Caption": "Générer une légende", "Settings Preset": "Preset de réglages:", "System Prompt Name": "Nom du prompt système:", "Instruct Mode": "Mode Instruction:", "Save and Update": "Sauvegarder et mettre à jour", "Don't ask again for this URL": "Ne plus demander pour cette URL"}