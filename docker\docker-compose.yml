services:
  sillytavern:
    build: ..
    container_name: sillytavern
    hostname: sillytavern
    image: ghcr.io/sillytavern/sillytavern:latest
    environment:
      - NODE_ENV=production
      - FORCE_COLOR=1
    ports:
      - "8000:8000"
    volumes:
      - "./config:/home/<USER>/app/config"
      - "./data:/home/<USER>/app/data"
      - "./plugins:/home/<USER>/app/plugins"
      - "./extensions:/home/<USER>/app/public/scripts/extensions/third-party"
    restart: unless-stopped
