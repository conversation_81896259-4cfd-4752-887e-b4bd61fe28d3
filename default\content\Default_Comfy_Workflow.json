{"3": {"class_type": "K<PERSON><PERSON><PERSON>", "inputs": {"cfg": "%scale%", "denoise": 1, "latent_image": ["5", 0], "model": ["4", 0], "negative": ["7", 0], "positive": ["6", 0], "sampler_name": "%sampler%", "scheduler": "%scheduler%", "seed": "%seed%", "steps": "%steps%"}}, "4": {"class_type": "CheckpointLoaderSimple", "inputs": {"ckpt_name": "%model%"}}, "5": {"class_type": "EmptyLatentImage", "inputs": {"batch_size": 1, "height": "%height%", "width": "%width%"}}, "6": {"class_type": "CLIPTextEncode", "inputs": {"clip": ["4", 1], "text": "%prompt%"}}, "7": {"class_type": "CLIPTextEncode", "inputs": {"clip": ["4", 1], "text": "%negative_prompt%"}}, "8": {"class_type": "VAEDecode", "inputs": {"samples": ["3", 0], "vae": ["4", 2]}}, "9": {"class_type": "SaveImage", "inputs": {"filename_prefix": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "images": ["8", 0]}}}