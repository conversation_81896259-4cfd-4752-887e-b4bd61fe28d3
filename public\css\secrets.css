.secretKeyManager {
    display: flex;
    flex-direction: column;
    gap: 5px;
    height: 100%;
}

.secretKeyManagerHeader {
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    gap: 5px;
}

.secretKeyManagerSubtitle {
    display: flex;
    flex-direction: row;
    justify-content: space-between;
    align-items: center;
    width: 100%;
}

.secretKeyManagerInfo {
    display: flex;
    flex-direction: column;
    align-items: baseline;
    flex: 1;
    font-size: 0.95em;
}

.secretKeyManagerList {
    display: flex;
    flex-direction: column;
    text-align: left;
    gap: 5px;
    flex: 1;
    overflow-y: auto;
    height: 100%;
}

.secretKeyManagerItem {
    border: 1px solid var(--SmartThemeBorderColor);
    padding: 5px 10px;
    border-radius: 10px;
    background-color: var(--black30a);
    display: flex;
    flex-direction: row;
    align-items: center;
}

.secretKeyManagerItem.active {
    background-color: var(--cobalt30a);
}

.secretKeyManagerItemInfo {
    display: flex;
    flex-direction: column;
    flex: 1;
    gap: 5px;
}

.secretKeyManagerItemSubtitle,
.secretKeyManagerItemHeader {
    word-break: break-all;
}

.secretKeyManagerItemId {
    text-decoration: underline;
    text-decoration-style: dotted;
    cursor: pointer;
}

.secretKeyManagerItemActions {
    display: flex;
    flex-direction: column;
    gap: 5px;
}

.secretKeyManagerItemActionsRow {
    display: flex;
    flex-direction: row;
    gap: 5px;
}

.secretKeyManagerItemActionsRow>button {
    margin: 0;
}

.secretKeyManagerList:empty {
    display: none;
}

.secretKeyManagerListEmpty {
    display: flex;
    justify-content: center;
    align-items: center;
    width: 100%;
    opacity: 0.8;
    font-weight: bold;
    font-size: 1.05em;
}
