{"temp": 0.7, "temperature_last": true, "top_p": 1, "top_k": 0, "top_a": 0.2, "tfs": 0.95, "epsilon_cutoff": 0, "eta_cutoff": 0, "typical_p": 1, "min_p": 0, "rep_pen": 1.15, "rep_pen_range": 0, "rep_pen_decay": 0, "rep_pen_slope": 1, "no_repeat_ngram_size": 0, "penalty_alpha": 0, "num_beams": 1, "length_penalty": 1, "min_length": 0, "encoder_rep_pen": 1, "freq_pen": 0, "presence_pen": 0, "skew": 0, "do_sample": true, "early_stopping": false, "dynatemp": false, "min_temp": 0, "max_temp": 2, "dynatemp_exponent": 1, "smoothing_factor": 0, "smoothing_curve": 1, "dry_allowed_length": 2, "dry_multiplier": 0, "dry_base": 1.75, "dry_sequence_breakers": "[\"\\n\", \":\", \"\\\"\", \"*\"]", "dry_penalty_last_n": 0, "add_bos_token": true, "ban_eos_token": false, "skip_special_tokens": true, "mirostat_mode": 0, "mirostat_tau": 5, "mirostat_eta": 0.1, "guidance_scale": 1, "negative_prompt": "", "grammar_string": "", "json_schema": {}, "banned_tokens": "", "sampler_priority": ["temperature", "dynamic_temperature", "quadratic_sampling", "top_k", "top_p", "typical_p", "epsilon_cutoff", "eta_cutoff", "tfs", "top_a", "min_p", "mi<PERSON><PERSON>"], "samplers": ["top_k", "tfs_z", "typical_p", "top_p", "min_p", "temperature"], "ignore_eos_token": false, "spaces_between_special_tokens": true, "speculative_ngram": false, "sampler_order": [6, 0, 1, 3, 4, 2, 5], "logit_bias": [], "rep_pen_size": 0}