{"input_sequence": "<|start_header_id|>user<|end_header_id|>\n\n", "output_sequence": "<|start_header_id|>assistant<|end_header_id|>\n\n", "last_output_sequence": "", "system_sequence": "<|start_header_id|>system<|end_header_id|>\n\n", "stop_sequence": "<|eot_id|>", "wrap": false, "macro": true, "names_behavior": "force", "activation_regex": "", "system_sequence_prefix": "", "system_sequence_suffix": "", "first_output_sequence": "", "skip_examples": false, "output_suffix": "<|eot_id|>", "input_suffix": "<|eot_id|>", "system_suffix": "<|eot_id|>", "user_alignment_message": "", "system_same_as_user": false, "last_system_sequence": "", "name": "Llama 3 Instruct"}