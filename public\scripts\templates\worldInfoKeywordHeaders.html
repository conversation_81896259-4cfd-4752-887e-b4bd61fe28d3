<div id="WIEntryHeaderTitlesPC" class="flex-container wide100p spaceBetween justifyCenter textAlignCenter" style="padding:0 7.0em;">
    <small class="flex1" data-i18n="Title/Memo">Title/Memo</small>
    <small style="width: calc(3.5em + 10px)" data-i18n="Strategy">Strategy</small>
    <small style="width: calc(3.5em + 20px)" data-i18n="Position">Position</small>
    <small style="width: calc(3.5em + 15px)" data-i18n="Depth">Depth</small>
    <small style="width: calc(3.5em + 20px)" data-i18n="Order">Order</small>
    <small style="width: calc(3.5em + 15px)" data-i18n="Trigger %">Trigger %</small>
</div>
