{"cells": [{"attachments": {}, "cell_type": "markdown", "metadata": {}, "source": ["**Links**<br>\n", "Extensions API GitHub: https://github.com/SillyTavern/SillyTavern-extras/<br>\n", "SillyTavern community Discord (support and discussion): https://discord.gg/sillytavern"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["#@title <-- Tap this if you run on Mobile { display-mode: \"form\" }\n", "#Taken from KoboldAI colab\n", "%%html\n", "<b>Press play on the audio player to keep the tab alive. (Uses only 13MB of data)</b><br/>\n", "<audio src=\"https://henk.tech/colabkobold/silence.m4a\" controls>"]}, {"cell_type": "code", "execution_count": null, "metadata": {"cellView": "form", "id": "lVftocpwCoYw"}, "outputs": [], "source": ["#@markdown (RECOMMENDED) Generates an API key for you to use with the API\n", "secure = False #@param {type:\"boolean\"}\n", "#@markdown Allows to run SillyTavern Extras on CPU (use if you're out of daily GPU allowance)\n", "use_cpu = False #@param {type:\"boolean\"}\n", "#@markdown Allows to run Stable Diffusion pipeline on CPU (slow!)\n", "use_sd_cpu = False #@param {type:\"boolean\"}\n", "#@markdown ***\n", "#@markdown Enables the WebSearch module\n", "extras_enable_websearch = True #@param {type:\"boolean\"}\n", "#@markdown ***\n", "#@markdown Loads the image captioning module\n", "extras_enable_caption = True #@param {type:\"boolean\"}\n", "captioning_model = \"Salesforce/blip-image-captioning-large\" #@param [ \"Salesforce/blip-image-captioning-large\", \"Salesforce/blip-image-captioning-base\" ]\n", "#@markdown * Salesforce/blip-image-captioning-large - good base model\n", "#@markdown * Salesforce/blip-image-captioning-base - slightly faster but less accurate\n", "#@markdown ***\n", "#@markdown Loads the sentiment classification model\n", "extras_enable_classify = True #@param {type:\"boolean\"}\n", "classification_model = \"nateraw/bert-base-uncased-emotion\" #@param [\"nateraw/bert-base-uncased-emotion\", \"joeddav/distilbert-base-uncased-go-emotions-student\"]\n", "#@markdown * nateraw/bert-base-uncased-emotion = 6 supported emotions<br>\n", "#@markdown * joeddav/distilbert-base-uncased-go-emotions-student = 28 supported emotions\n", "#@markdown ***\n", "#@markdown Loads the story summarization module\n", "extras_enable_summarize = True #@param {type:\"boolean\"}\n", "summarization_model = \"slauw87/bart_summarisation\" #@param [ \"slauw87/bart_summarisation\", \"Qiliang/bart-large-cnn-samsum-ChatGPT_v3\", \"Qiliang/bart-large-cnn-samsum-ElectrifAi_v10\", \"distilbart-xsum-12-3\" ]\n", "#@markdown * slauw87/bart_summarisation - general purpose summarization model\n", "#@markdown * Qiliang/bart-large-cnn-samsum-ChatGPT_v3 - summarization model optimized for chats\n", "#@markdown * Qiliang/bart-large-cnn-samsum-ElectrifAi_v10 - nice results so far, but still being evaluated\n", "#@markdown * distilbart-xsum-12-3 - faster, but pretty basic alternative\n", "#@markdown ***\n", "#@markdown Enables Silero text-to-speech module\n", "extras_enable_silero_tts = True #@param {type:\"boolean\"}\n", "#@markdown Enables Microsoft Edge text-to-speech module\n", "extras_enable_edge_tts = True #@param {type:\"boolean\"}\n", "#@markdown Enables RVC module\n", "extras_enable_rvc = False #@param {type:\"boolean\"}\n", "#@markdown ***\n", "#@markdown Enables Whisper speech recognition module\n", "extras_enable_whisper_stt = True #@param {type:\"boolean\"}\n", "whisper_model = \"base.en\" #@param [ \"tiny.en\", \"base.en\", \"small.en\", \"medium.en\", \"tiny\", \"base\", \"small\", \"medium\", \"large\" ]\n", "#@markdown There are five model sizes, four with English-only versions, offering speed and accuracy tradeoffs.\n", "#@markdown The .en models for English-only applications tend to perform better, especially for the tiny.en and base.en models.\n", "#@markdown ***\n", "#@markdown Enables SD picture generation\n", "extras_enable_sd = True #@param {type:\"boolean\"}\n", "sd_model = \"ckpt/anything-v4.5-vae-swapped\" #@param [ \"ckpt/anything-v4.5-vae-swapped\", \"hakurei/waifu-diffusion\", \"philz1337/clarity\", \"prompthero/openjourney\", \"ckpt/sd15\", \"stabilityai/stable-diffusion-2-1-base\" ]\n", "#@markdown * ckpt/anything-v4.5-vae-swapped - anime style model\n", "#@markdown * hakurei/waifu-diffusion - anime style model\n", "#@markdown * philz1337/clarity - realistic style model\n", "#@markdown * prompthero/openjourney - midjourney style model\n", "#@markdown * ckpt/sd15 - base SD 1.5\n", "#@markdown * stabilityai/stable-diffusion-2-1-base - base SD 2.1\n", "#@markdown ***\n", "#@markdown Enables ChromaDB module\n", "extras_enable_chromadb = True #@param {type:\"boolean\"}\n", "\n", "import subprocess\n", "import secrets\n", "\n", "# ---\n", "# SillyTavern extras\n", "extras_url = '(disabled)'\n", "params = []\n", "if use_cpu:\n", "    params.append('--cpu')\n", "if use_sd_cpu:\n", "    params.append('--sd-cpu')\n", "if secure:\n", "    params.append('--secure')\n", "params.append('--share')\n", "modules = []\n", "\n", "if extras_enable_caption:\n", "  modules.append('caption')\n", "if extras_enable_summarize:\n", "  modules.append('summarize')\n", "if extras_enable_classify:\n", "  modules.append('classify')\n", "if extras_enable_sd:\n", "  modules.append('sd')\n", "if extras_enable_silero_tts:\n", "  modules.append('silero-tts')\n", "if extras_enable_edge_tts:\n", "  modules.append('edge-tts')\n", "if extras_enable_chromadb:\n", "  modules.append('chromadb')\n", "if extras_enable_whisper_stt:\n", "  modules.append('whisper-stt')\n", "  params.append(f'--stt-whisper-model-path={whisper_model}')\n", "if extras_enable_rvc:\n", "  modules.append('rvc')\n", "  params.append('--max-content-length=2000')\n", "  params.append('--rvc-save-file')\n", "\n", "\n", "if extras_enable_websearch:\n", "    print(\"Enabling WebSearch module\")\n", "    modules.append('websearch')\n", "    !apt update\n", "    !apt install -y chromium-chromedriver\n", "\n", "params.append(f'--classification-model={classification_model}')\n", "params.append(f'--summarization-model={summarization_model}')\n", "params.append(f'--captioning-model={captioning_model}')\n", "params.append(f'--sd-model={sd_model}')\n", "params.append(f'--enable-modules={\",\".join(modules)}')\n", "\n", "\n", "%cd /\n", "!git clone https://github.com/SillyTavern/SillyTavern-extras\n", "%cd /SillyTavern-extras\n", "!git clone https://github.com/Cohee1207/tts_samples\n", "!npm install -g localtunnel\n", "%pip install -r requirements.txt\n", "!wget https://github.com/cloudflare/cloudflared/releases/download/2023.5.0/cloudflared-linux-amd64 -O /tmp/cloudflared-linux-amd64\n", "!chmod +x /tmp/cloudflared-linux-amd64\n", "\n", "if extras_enable_rvc:\n", "  print(\"Installing RVC requirements\")\n", "  %pip install -r requirements-rvc.txt\n", "\n", "# Generate a random API key\n", "api_key = secrets.token_hex(5)\n", "\n", "# Write the API key to api_key.txt\n", "with open('./api_key.txt', 'w') as f:\n", "    f.write(api_key)\n", "print(f\"API Key generated: {api_key}\")\n", "\n", "cmd = f\"python server.py {' '.join(params)}\"\n", "print(cmd)\n", "extras_process = subprocess.Popen(\n", "    cmd, stdout=subprocess.PIPE, stderr=subprocess.STDOUT, cwd='/SillyTavern-extras', shell=True)\n", "print('processId:', extras_process.pid)\n", "while True:\n", "    line = extras_process.stdout.readline().decode().strip()\n", "    if line != None and line != '':\n", "        print(line)\n"]}], "metadata": {"accelerator": "GPU", "colab": {"private_outputs": true, "provenance": []}, "gpuClass": "standard", "kernelspec": {"display_name": "Python 3", "name": "python3"}, "language_info": {"name": "python"}}, "nbformat": 4, "nbformat_minor": 0}