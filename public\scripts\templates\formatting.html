<span data-i18n="help_format_1">Text formatting commands:</span>
<ul> <!-- I know it seems like all these 'display as' could fit into just a couple entries, but other languages might need it with their grammar
Actually I feel that languages with SOV might need even more extra entries, but let's KISS it for now, those can be easily added in the future without breaking anything -->
    <li><tt data-i18n="help_format_2">*text*</tt> - <span data-i18n="help_format_3">displays as </span><i data-i18n="help_format_4">italics</i></li>
    <li><tt data-i18n="help_format_5">**text**</tt> - <span data-i18n="help_format_6">displays as </span><b data-i18n="help_format_7">bold</b></li>
    <li><tt data-i18n="help_format_8">***text***</tt> - <span data-i18n="help_format_9">displays as </span><b><i data-i18n="help_format_10">bold italics</i></b></li>
    <li><tt data-i18n="help_format_11">__text__</tt> - <span data-i18n="help_format_12">displays as an </span><u data-i18n="help_format_13">underline</u></li>
    <li><tt data-i18n="help_format_14">~~text~~</tt> - <span data-i18n="help_format_15">displays as a </span><del data-i18n="help_format_16">strikethough</del></li>
    <li><tt data-i18n="help_format_17">[text](url)</tt> - <span data-i18n="help_format_18">displays as a </span><a href="#" data-i18n="help_format_19">hyperlink</a></li>
    <li><tt data-i18n="help_format_20">![text](url)</tt> - <span data-i18n="help_format_21">displays as an image</span></li>
    <li><tt data-i18n="help_format_22">```text```</tt> - <span data-i18n="help_format_23">displays as a code block (new lines allowed between the backticks)</span></li>
</ul>
<pre><code data-i18n="help_format_like_this"> like this</code></pre>
<ul>
    <li><tt data-i18n="help_format_24">`text`</tt> - <span data-i18n="help_format_25">displays as </span><code data-i18n="help_format_26">inline code</code></li>
    <li><tt data-i18n="help_format_27">&gt; text</tt> - <span data-i18n="help_format_28">displays as a blockquote (note the space after &gt;)</span></li>
    <blockquote data-i18n="help_format_like_this">like this</blockquote>
    <li><tt data-i18n="help_format_29"># text</tt> - <span data-i18n="help_format_30">displays as a large header (note the space)</span></li>
    <h1 data-i18n="help_format_like_this">like this</h1>
    <li><tt data-i18n="help_format_32">## text</tt> - <span data-i18n="help_format_33">displays as a medium header (note the space)</span></li>
    <h2 data-i18n="help_format_like_this">like this</h2>
    <li><tt data-i18n="help_format_35">### text</tt> - <span data-i18n="help_format_36">displays as a small header (note the space)</span></li>
    <h3 data-i18n="help_format_like_this">like this</h3>
</ul>
