name: Update i18n data

on: workflow_dispatch

jobs:
  build:
    runs-on: ubuntu-latest
    permissions:                # Job-level permissions configuration starts here
      contents: write           # 'write' access to repository contents
    steps:
    - name: disable auto crlf
      uses: steve02081504/disable-autocrlf@v1
    - uses: actions/checkout@v4
      with:
        ref: ${{ github.head_ref }}
        fetch-depth: 0 # otherwise, there would be errors pushing refs to the destination repository.
    - name: Create local changes
      run: |
        aria2c https://raw.githubusercontent.com/SillyTavern/SillyTavern-i18n/main/generate.py
        aria2c https://raw.githubusercontent.com/SillyTavern/SillyTavern-i18n/main/requirements.txt
        pip install -r ./requirements.txt
        python ./generate.py "" --sort-keys
        rm -f ./generate.py ./requirements.txt
    - name: add all
      run: git add -A
    - name: push
      uses: actions-go/push@master
      with:
        author-email: 41898282+github-actions[bot]@users.noreply.github.com
        author-name: github-actions[bot]
        commit-message: 'i18n changes'
        remote: origin
