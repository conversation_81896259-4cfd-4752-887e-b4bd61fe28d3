<div class="chat_world range-block flexFlowColumn flex-container">
    <div class="range-block-title">
        <h4 data-i18n="Chat Lorebook"><!-- This data-i18n attribute is kept for backward compatibility, use the ones below when translating -->
            <span data-i18n="Chat Lorebook for">Chat Lorebook for</span> <span class="chat_name"></span>
        </h4>
    </div>
    <div class="range-block-counter justifyLeft flex-container flexFlowColumn margin-bot-10px">
        <span data-i18n="chat_world_template_txt">
            A selected World Info will be bound to this chat. When generating an AI reply,
            it will be combined with the entries from global and character lorebooks.
        </span>
    </div>
    <div class="range-block-range wide100p">
        <select class="chat_world_info_selector wide100p">
            <option value="">--- None ---</option>
        </select>
    </div>
</div>
