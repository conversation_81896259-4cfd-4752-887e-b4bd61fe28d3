<div class="regex-script-label flex-container flexnowrap">
    <input type="checkbox" class="regex_bulk_checkbox" />
    <span class="drag-handle menu-handle">&#9776;</span>
    <div class="regex_script_name flexGrow overflow-hidden"></div>
    <div class="flex-container flexnowrap">
        <label class="checkbox flex-container" for="regex_disable">
            <input type="checkbox" name="regex_disable" class="disable_regex" />
            <span class="regex-toggle-on fa-solid fa-toggle-on" data-i18n="[title]ext_regex_disable_script" title="Disable script"></span>
            <span class="regex-toggle-off fa-solid fa-toggle-off" data-i18n="[title]ext_regex_enable_script" title="Enable script"></span>
        </label>
        <div class="edit_existing_regex menu_button" data-i18n="[title]ext_regex_edit_script" title="Edit script">
            <i class="fa-solid fa-pencil"></i>
        </div>
        <div class="move_to_global menu_button" data-i18n="[title]ext_regex_move_to_global" title="Move to global scripts">
            <i class="fa-solid fa-arrow-up"></i>
        </div>
        <div class="move_to_scoped menu_button" data-i18n="[title]ext_regex_move_to_scoped" title="Move to scoped scripts">
            <i class="fa-solid fa-arrow-down"></i>
        </div>
        <div class="export_regex menu_button" data-i18n="[title]ext_regex_export_script" title="Export script">
            <i class="fa-solid fa-file-export"></i>
        </div>
        <div class="delete_regex menu_button" data-i18n="[title]ext_regex_delete_script" title="Delete script">
            <i class="fa-solid fa-trash"></i>
        </div>
    </div>
</div>
