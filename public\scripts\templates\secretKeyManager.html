<div class="secretKeyManager">
    <div class="secretKeyManagerHeader">
        <div class="secretKeyManagerSubtitle">
            <div class="secretKeyManagerInfo">
                <div class="flex-container">
                    <div data-i18n="API:">API:</div>
                    <span>{{name}}</span>
                </div>
                <div class="flex-container">
                    <div data-i18n="Key:">Key:</div>
                    <code>{{key}}</code>
                </div>
            </div>
            <div class="secretKeyManagerActions">
                <button class="menu_button menu_button_icon" data-action="add-secret">
                    <i class="fa-solid fa-plus"></i>
                    <span data-i18n="Add Secret">Add Secret</span>
                </button>
            </div>
        </div>
    </div>
    <hr>
    <div class="secretKeyManagerList"></div>
    <div class="secretKeyManagerListEmpty">
        <span data-i18n="No secrets saved.">No secrets saved.</span>
    </div>
</div>
