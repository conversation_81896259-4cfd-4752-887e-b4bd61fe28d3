.world_info_select_block {
    display: flex;
    flex-direction: row;
    align-items: baseline;
    gap: 5px;
}

.budget_cap_note {
    flex-basis: 100%;
    line-height: 0.1;
}

#world_popup {
    min-height: 100px;
    min-width: 100px;
    left: 0;
    right: 0;
    flex-direction: column;
    z-index: 3010;
    overflow-y: hidden;
}

.WIEntryContentAndMemo {
    width: 100% !important;
    flex-wrap: nowrap !important;
}

.WIEntryContentAndMemo .world_entry_thin_controls {
    flex: 1;
}

#world_popup_bottom_holder {
    display: flex;
    flex-flow: row;
    justify-content: space-evenly;
    align-items: center;
}

#world_popup_bottom_holder div {
    width: fit-content;
    user-select: none;
}

#form_rename_world {
    display: flex;
    align-items: center;
    gap: 5px;
}

.world_popup_expander {
    flex-grow: 1;
}

#world_popup_entries_list {
    flex-grow: 1;
    overflow-y: auto;
}

#world_popup_entries_list:empty {
    width: 100%;
    height: 100%;
}

#world_popup_entries_list:empty::before {
    content: 'No entries found.';
    font-size: calc(var(--mainFontSize) + .1rem);
    font-weight: bolder;
    width: 100%;
    height: 100%;
    display: flex;
    justify-content: center;
    align-items: center;
    opacity: 0.8;
}

.world_entry_form_control {
    display: flex;
    flex-direction: column;
    position: relative;
}

.world_entry_form_control .keyprimarytextpole,
.world_entry_form_control .keysecondarytextpole {
    padding-right: 25px;
}

.world_entry_thin_controls {
    display: flex;
    flex-direction: row;
}

/* .world_entry_thin_controls>div {
    flex: 1;
} */

.world_entry_form_control label h4 {
    margin-bottom: 0;
    margin-top: 0;
}

.world_entry_form_control label h5 {
    margin-top: 3px;
    margin-bottom: 3px;
}

.world_entry_form_control textarea {
    height: auto;
    margin-top: 0;
    margin-bottom: 0;
}

.delete_entry_button {
    height: min-content;
}

.world_entry_form_control.world_entry_form_horizontal {
    flex-direction: row;
    align-items: center;
    flex-wrap: wrap;
}

.world_entry .inline-drawer-header {
    cursor: initial;
}

.world_entry .inline-drawer-header-pointer {
    cursor: pointer;
}

.world_entry .killSwitch {
    cursor: pointer;
}

.world_entry_form_control input[type=button] {
    cursor: pointer;
}

.world_entry_form_horizontal h5 {
    margin: 0 1rem;
}

.world_entry_form_control .checkbox {
    align-items: center;
    display: flex;
    flex-direction: row;
    column-gap: 10px;
}

.world_entry_form_control .checkbox h4 {
    margin: 0;
    display: inline-block;
}

.world_entry_form_radios label {
    margin-left: 0;
}

.world_entry_form_radios h4 {
    display: inline;
}

#world_popup h5 {
    color: var(--grey70);
}

/* possible place for WI Entry header styling */
/* .world_entry_form .inline-drawer-header {
    background-color: var(--SmartThemeShadowColor);
} */

#world_editor_select {
    text-overflow: ellipsis;
    white-space: nowrap;
    width: 10em;
}

#world_info_search {
    width: 10em;
    min-width: 10em;
    flex-grow: 1;
}

#world_info_sort_order {
    width: 7em;
}

.world_entry .killSwitch.fa-toggle-on {
    color: var(--SmartThemeQuoteColor);
}

.wi-card-entry {
    border: 1px solid;
    border-color: var(--SmartThemeBorderColor);
    border-radius: 10px;
    padding: 0 5px;
    margin-bottom: 1px;
}

.world_entry {
    transition: opacity var(--animation-duration-3x);
}

.world_entry_edit {
    display: contents;
}

.disabledWIEntry {
    opacity: 0.4;
    filter: grayscale(1);
}

.disabledWIEntry:not(input):hover {
    opacity: 1;
    filter: grayscale(0.5);
}

.height32px {
    height: 32px;
}

.WIEntryHeaderTitleMobile {
    display: none;
}

span.select2-container .select2-selection__choice__display:has(> .regex_item),
span.select2-container .select2-results__option:has(> .result_block .regex_item) {
    background-color: #D27D2D30;
}

.regex_item .regex_icon {
    background-color: var(--black30a);
    color: var(--SmartThemeBodyColor);
    border: 1px solid var(--SmartThemeBorderColor);
    border-radius: 7px;
    font-weight: bold;
    font-size: calc(var(--mainFontSize) * 0.75);
    padding: 0px 3px;
    position: relative;
    top: -1px;
    margin-right: 3px;
}

.select2-results__option .regex_item .regex_icon {
    margin-right: 6px;
}

.select2-results__option .item_count {
    margin-left: 10px;
    float: right;
}

select.keyselect+span.select2-container .select2-selection--multiple {
    padding-right: 30px;
}

.switch_input_type_icon {
    cursor: pointer;
    font-weight: bold;
    height: 20px;
    width: fit-content;
    margin-right: 5px;
    margin-top: calc(5px + var(--mainFontSize));
    position: absolute;
    right: 0;
    padding: 1px;

    background-color: transparent;
    border: none;
    font-size: 1em;

    opacity: 0.5;
    color: var(--SmartThemeBodyColor);
    transition: opacity var(--animation-duration-2x);
}

.switch_input_type_icon:hover {
    opacity: 1;
}

#wiCheckboxes {
    align-self: center;
    width: 100%;
}

.world_entry:has(input[name="delay_until_recursion"]:not(:checked)) .world_entry_form_control:has(input[name="delayUntilRecursionLevel"]) {
    display: none;
}

.world_entry label[for="__invisible"] {
    visibility: hidden;
    pointer-events: none;
    opacity: 0;
    width: 0;
    overflow: hidden;
}

#WIMultiSelector .select2-container .select2-selection--multiple {
    max-height: 25vh;
    overflow-y: auto;
    overflow-x: hidden;
}
