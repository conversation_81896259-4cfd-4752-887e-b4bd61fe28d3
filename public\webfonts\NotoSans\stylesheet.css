@font-face {
    font-family: 'Noto Sans';
    src: url('NotoSans-Black.woff2') format('woff2'),
        url('NotoSans-Black.woff') format('woff');
    font-weight: 900;
    font-style: normal;
    font-display: swap;
}

@font-face {
    font-family: 'Noto Sans';
    src: url('NotoSans-ExtraBoldItalic.woff2') format('woff2'),
        url('NotoSans-ExtraBoldItalic.woff') format('woff');
    font-weight: bold;
    font-style: italic;
    font-display: swap;
}

@font-face {
    font-family: 'Noto Sans';
    src: url('NotoSans-BlackItalic.woff2') format('woff2'),
        url('NotoSans-BlackItalic.woff') format('woff');
    font-weight: 900;
    font-style: italic;
    font-display: swap;
}

@font-face {
    font-family: 'Noto Sans';
    src: url('NotoSans-ExtraBold.woff2') format('woff2'),
        url('NotoSans-ExtraBold.woff') format('woff');
    font-weight: bold;
    font-style: normal;
    font-display: swap;
}

@font-face {
    font-family: 'Noto Sans';
    src: url('NotoSans-ThinItalic.woff2') format('woff2'),
        url('NotoSans-ThinItalic.woff') format('woff');
    font-weight: 100;
    font-style: italic;
    font-display: swap;
}

@font-face {
    font-family: 'Noto Sans';
    src: url('NotoSans-BoldItalic.woff2') format('woff2'),
        url('NotoSans-BoldItalic.woff') format('woff');
    font-weight: bold;
    font-style: italic;
    font-display: swap;
}

@font-face {
    font-family: 'Noto Sans';
    src: url('NotoSans-Bold.woff2') format('woff2'),
        url('NotoSans-Bold.woff') format('woff');
    font-weight: bold;
    font-style: normal;
    font-display: swap;
}

@font-face {
    font-family: 'Noto Sans';
    src: url('NotoSans-LightItalic.woff2') format('woff2'),
        url('NotoSans-LightItalic.woff') format('woff');
    font-weight: 300;
    font-style: italic;
    font-display: swap;
}

@font-face {
    font-family: 'Noto Sans';
    src: url('NotoSans-Italic.woff2') format('woff2'),
        url('NotoSans-Italic.woff') format('woff');
    font-weight: normal;
    font-style: italic;
    font-display: swap;
}

@font-face {
    font-family: 'Noto Sans';
    src: url('NotoSans-ExtraLightItalic.woff2') format('woff2'),
        url('NotoSans-ExtraLightItalic.woff') format('woff');
    font-weight: 200;
    font-style: italic;
    font-display: swap;
}

@font-face {
    font-family: 'Noto Sans';
    src: url('NotoSans-Light.woff2') format('woff2'),
        url('NotoSans-Light.woff') format('woff');
    font-weight: 300;
    font-style: normal;
    font-display: swap;
}

@font-face {
    font-family: 'Noto Sans';
    src: url('NotoSans-ExtraLight.woff2') format('woff2'),
        url('NotoSans-ExtraLight.woff') format('woff');
    font-weight: 200;
    font-style: normal;
    font-display: swap;
}

@font-face {
    font-family: 'Noto Sans';
    src: url('NotoSans-Medium.woff2') format('woff2'),
        url('NotoSans-Medium.woff') format('woff');
    font-weight: 500;
    font-style: normal;
    font-display: swap;
}

@font-face {
    font-family: 'Noto Sans';
    src: url('NotoSans-Regular.woff2') format('woff2'),
        url('NotoSans-Regular.woff') format('woff');
    font-weight: normal;
    font-style: normal;
    font-display: swap;
}

@font-face {
    font-family: 'Noto Sans';
    src: url('NotoSans-MediumItalic.woff2') format('woff2'),
        url('NotoSans-MediumItalic.woff') format('woff');
    font-weight: 500;
    font-style: italic;
    font-display: swap;
}

@font-face {
    font-family: 'Noto Sans';
    src: url('NotoSans-SemiBoldItalic.woff2') format('woff2'),
        url('NotoSans-SemiBoldItalic.woff') format('woff');
    font-weight: 600;
    font-style: italic;
    font-display: swap;
}

@font-face {
    font-family: 'Noto Sans';
    src: url('NotoSans-SemiBold.woff2') format('woff2'),
        url('NotoSans-SemiBold.woff') format('woff');
    font-weight: 600;
    font-style: normal;
    font-display: swap;
}

@font-face {
    font-family: 'Noto Sans';
    src: url('NotoSans-Thin.woff2') format('woff2'),
        url('NotoSans-Thin.woff') format('woff');
    font-weight: 100;
    font-style: normal;
    font-display: swap;
}

