/*will apply to anything 1000px or less. this catches ipads, horizontal phones, and vertical phones)*/
@media screen and (max-width: 1000px) {

    #UI-Theme-Block,
    #UI-Customization,
    #power-user-options-block,
    #ContextSettings,
    #InstructSettingsColumn,
    #InstructSequencesColumn {
        flex-basis: 100%;
    }


    #send_form.compact #leftSendForm,
    #send_form.compact #rightSendForm {
        flex-wrap: nowrap;
        width: unset;
    }

    #sheldWidthToggleBlock {
        display: none;
    }

    .bg_button {
        font-size: 15px;
    }

    #extensions_settings,
    #extensions_settings2 {
        width: 100% !important;
        min-width: 100% !important;
    }

    body:not(.waifuMode) .zoomed_avatar {
        min-width: 100px;
        min-height: 100px;
        position: absolute;
        padding: 0;
        filter: drop-shadow(2px 2px 2px #51515199);
        z-index: 30;
        overflow: hidden;
        right: 0;
        width: fit-content;
        max-height: calc(60vh - 60px);
        max-height: calc(60dvh - 60px);
        max-width: 90vw;
        max-width: 90dvw;
        left: 50%;
        top: 50%;
        transform: translateX(-50%) translateY(-50%);
        align-items: center;
        justify-content: center;
        height: fit-content;
        width: 100%;
    }

    .zoomed_avatar .dragClose {
        display: unset;
    }

    /* .world_entry_thin_controls, */
    #persona-management-block,
    #character_popup .flex-container {
        flex-direction: column;
    }

    #WIMultiSelector {
        align-self: normal;
    }

    .WIEntryContentAndMemo {
        flex-flow: column;
    }

    .WIEntryContentAndMemo .world_entry_thin_controls {
        width: 100%;
    }

    .world_entry_form_control.world_entry_form_horizontal {
        /* flex-direction: column; */
        align-items: flex-start;
        row-gap: 0.5rem;
    }

    .world_entry_form_control.world_entry_form_horizontal .world_popup_expander {
        display: none;
    }

    .world_entry .inline-drawer-toggle {
        padding-bottom: 5px;
    }

    #worldInfoScanningCheckboxes {
        flex-flow: row;
        flex-wrap: wrap;
    }

    body {
        touch-action: none;
        overflow: hidden;
        position: fixed;
    }

    .world_entry_form_control {
        /* width: 100%; */
    }

    .drawer-content {
        min-width: unset;
        width: 100dvw;
        max-height: calc(100vh - 45px);
        max-height: calc(100dvh - 45px);
        position: fixed;
        left: 0;
        top: 5px;
        border: 1px solid var(--SmartThemeBorderColor);
    }

    .drawer-content .floating_panel_maximize,
    .drawer-content .inline-drawer-maximize {
        display: none;
    }

    #select_chat_popup {
        align-items: start;
        height: min-content;
        align-content: start;
        max-width: unset;
    }

    #wiActivationSettings,
    #wiTopBlock {
        flex-direction: column;
    }

    #top-settings-holder,
    #top-bar {
        position: fixed;
        width: 100vw;
        width: 100dvw;
    }

    #bg1,
    #bg_custom {
        height: 100vh !important;
        height: 100dvh !important;
        width: 100vw !important;
        width: 100dvw !important;
        background-position: center;

    }


    #sheld,
    #character_popup,
    .drawer-content {
        width: 100dvw !important;
        margin: 0 auto;
        max-width: 100dvw;
        left: 0 !important;
        resize: none !important;
        top: var(--topBarBlockSize);
    }

    .wi-settings {
        flex-direction: column;
        gap: 5px !important;
    }

    .WIEntryTitleAndStatus,
    .WIEntryHeaderControls {
        width: 100%;
    }

    #WIEntryHeaderTitlesPC {
        display: none;
    }

    .WIEntryHeaderTitleMobile {
        display: block !important;
    }

    #character_popup,
    #world_popup {
        overflow-y: auto;
    }

    #character_popup,
    #send_form {
        border: 1px solid var(--SmartThemeBorderColor);
        backdrop-filter: blur(calc(var(--SmartThemeBlurStrength) * 2));
        max-width: 100dvw;
    }

    #chat {
        border-left: 1px solid var(--SmartThemeBorderColor);
        border-right: 1px solid var(--SmartThemeBorderColor);
        border-bottom: 1px solid var(--SmartThemeBorderColor);
        align-items: start;
        align-content: start;
        overflow-y: auto;
        overflow-x: hidden
    }

    .mes_buttons {
        font-size: calc(var(--mainFontSize)*1.2);
    }

    .drag-grabber,
    .pull-tab {
        display: none !important;

    }

    #groupCurrentMemberPopoutButton,
    #summaryExtensionPopoutButton {
        display: none;
    }

    #right-nav-panel,
    #left-nav-panel,
    #floatingPrompt,
    #cfgConfig,
    #logprobsViewer,
    #movingDivs>div {
        /* 100vh are fallback units for browsers that don't support dvh */
        height: calc(100vh - 45px);
        height: calc(100dvh - 45px);
        min-width: 100dvw !important;
        width: 100dvw !important;
        max-width: 100dvw !important;
        overflow-y: hidden;
        border-left: 1px solid var(--SmartThemeBorderColor);
        border-right: 1px solid var(--SmartThemeBorderColor);
        border-bottom: 1px solid var(--SmartThemeBorderColor);
        border-radius: 0 0 20px 20px;
        top: var(--topBarBlockSize) !important;
        left: 0 !important;
        backdrop-filter: blur(calc(var(--SmartThemeBlurStrength) * 2));

        @starting-style {
            height: 0;
        }
    }

    #left-nav-panel:not(.openDrawer),
    #right-nav-panel:not(.openDrawer) {
        height: 0;
    }

    /*
    #right-nav-panel {
        padding-right: 15px;
    }
    */

    #floatingPrompt,
    #cfgConfig,
    #logprobsViewer,
    #movingDivs>div {
        height: min-content;
    }

    #right-nav-panel h4 {
        margin: 5px auto;
    }

    #result_info {
        font-size: calc(var(--mainFontSize) - .1rem);
    }

    /*     .avatar_div {
        margin-top: 5px;
    } */

    #character_popup {
        width: 100%;
        border-radius: 0 0 20px 20px;
        margin-top: 0px;
        height: calc(100% - var(--topBarBlockSize));
    }

    .drawer25pWidth {
        flex-basis: max(calc(100% / 4 - 10px), 190px);
    }

    .drawer33pWidth {
        flex-basis: max(calc(100% / 3 - 10px), 190px);
    }

    .expression-holder {
        display: none;
    }

    body.waifuMode #sheld {
        height: 40vh;
        height: 40dvh;
        top: 60vh;
        top: 60dvh;
        bottom: 0 !important;
    }

    body:not(.waifuMode) #expression-wrapper {
        visibility: hidden;
    }

    #visual-novel-wrapper {
        position: unset !important;
    }

    body.waifuMode .expression-holder {
        /*display: inline;*/

        max-width: 100vw;
        height: 100vh;
        width: max-content;
        margin: 0 auto;
        position: absolute;
        left: 0;
        right: 0;
        filter: drop-shadow(2px 2px 2px #51515199);
        z-index: 1 !important;
    }

    body.waifuMode img.expression {
        object-fit: cover;
    }

    body.waifuMode .zoomed_avatar_container {
        height: 100%;
    }

    body.waifuMode .zoomed_avatar {
        width: fit-content;
        max-height: calc(60vh - 60px);
        max-height: calc(60dvh - 60px);
        max-width: 90vw;
        max-width: 90dvw;
    }

    .scrollableInner {
        overflow-y: auto;
        overflow-x: hidden;
        max-height: calc(100vh - 90px);
        max-height: calc(100dvh - 90px);
    }

    .horde_multiple_hint {
        display: none;
    }

    .bg_list {
        width: unset;
    }
}

/*landscape mode phones and ipads*/
@media screen and (max-width: 1000px) and (orientation: landscape) {
    body.waifuMode img.expression {
        object-fit: contain;
    }

    .tag.excluded:after {
        top: unset;
        bottom: unset;
    }

    body:not(.waifuMode) .zoomed_avatar {
        max-height: calc(60vh - 60px);
        max-height: calc(60dvh - 60px);
        max-width: 90vw;
        max-width: 90dvw;
        left: 50%;
        top: 50%;
        transform: translateX(-50%) translateY(-50%);
        align-items: center;
        justify-content: center;
        height: fit-content;
        width: 100%;
    }
}

/*portrait mode phones*/
@media screen and (max-width: 450px) {

    body:not(.waifuMode) .zoomed_avatar {
        min-width: 100px;
        min-height: 100px;
        max-height: 50vh;
        max-width: 90vw;
        position: absolute;
        padding: 0;
        filter: drop-shadow(2px 2px 2px #51515199);
        z-index: 30;
        overflow: hidden;
        display: none;
        right: 0;
        left: 50%;
        top: 50%;
        transform: translateX(-50%) translateY(-50%);
        align-items: center;
        justify-content: center;
        height: fit-content;
        width: 100%;
    }

    .drawer25pWidth {
        flex-basis: max(calc(100% / 2 - 10px), 180px);
    }

    .drawer33pWidth {
        flex-basis: max(calc(100% / 2 - 10px), 180px);
    }

    .BGSampleTitle {
        display: none;
    }

    .tag.excluded:after {
        top: unset;
        bottom: unset;
    }


    #leftSendForm,
    #rightSendForm {
        width: 1.15em;
        flex-wrap: wrap;
        height: unset;
    }
}

/*iOS specific*/
@supports (-webkit-touch-callout: none) {
    body {
        margin: 0 auto;
    }

    #top-bar {
        width: 100vw;
    }

    #sheld {
        margin: unset;
        padding: unset;
        width: unset;
        height: unset;
        min-width: unset;
        max-width: unset;
        min-height: unset;
        max-height: unset;
        width: 100vw;
        width: 100dvw;
        height: calc(100vh - 36px);
        height: calc(100dvh - 36px);
        padding-right: max(env(safe-area-inset-right), 0px);
        padding-left: max(env(safe-area-inset-left), 0px);
        padding-bottom: 0;
    }

    body.PWA #sheld {
        padding-right: max(env(safe-area-inset-right), 2px);
        padding-left: max(env(safe-area-inset-left), 2px);
        padding-bottom: max(env(safe-area-inset-bottom), 15px);

    }

    #character_popup,
    #world_popup,
    #left-nav-panel,
    #right-nav-panel,
    .drawer-content {
        width: unset;
        height: unset;
        min-width: unset;
        max-width: unset;
        min-height: unset;
        max-height: unset;
        backdrop-filter: blur(calc(var(--SmartThemeBlurStrength) * 2));
        left: 0;
        right: 0;
        top: 0;
        margin: 0 auto;
        height: calc(100vh - 70px);
        height: calc(100dvh - 70px);
        width: calc(100dvw - 5px);
        max-height: calc(100vh - 70px);
        max-height: calc(100dvh - 70px);
        max-width: calc(100dvw - 5px);

    }

    #character_popup,
    #world_popup,
    .drawer-content {
        margin-top: 36px;
    }

    .scrollableInner {
        overflow-y: auto;
        overflow-x: hidden;
    }

    #horde_model {
        height: unset;
    }
}
